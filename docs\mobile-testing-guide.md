# Naroop Mobile Testing Guide

## Phase 1 Complete: Windows Preparation ✅

Your Naroop project is now fully prepared for iOS development! Here's what we've accomplished:

### ✅ Completed Setup

1. **Capacitor Installation & Configuration**
   - Installed @capacitor/core, @capacitor/cli, @capacitor/ios
   - Created iOS project structure
   - Configured for both development and production

2. **Mobile WebView Optimization**
   - Updated CORS settings for Capacitor origins
   - Added mobile-specific meta tags
   - Created PWA manifest for app-like behavior

3. **App Assets & Metadata**
   - Generated placeholder app icons (SVG format)
   - Created manifest.json with proper app metadata
   - Set up app shortcuts and branding

4. **Build Scripts**
   - Added Capacitor build commands to package.json
   - Created icon generation script
   - Set up sync and copy workflows

## Testing on Windows (Before iOS Build)

### Browser Mobile Simulation
Test your responsive design using browser dev tools:

1. **Chrome DevTools**
   ```
   F12 → Toggle Device Toolbar → Select iPhone/iPad
   Test: iPhone 14 Pro, iPhone SE, iPad
   ```

2. **Key Test Areas**
   - Touch-friendly button sizes (minimum 44px)
   - Readable text without zooming
   - Proper viewport scaling
   - Smooth scrolling and animations
   - Form input behavior

### Local Server Testing
```bash
# Start your development server
npm run dev

# Test in browser mobile view
# Navigate to: http://localhost:3000
```

### Capacitor Sync Test
```bash
# Copy web assets to iOS project
npm run cap:copy

# Verify no errors in terminal output
```

## Next Steps: Cloud Mac Setup

When you're ready to build the actual iOS app:

### Option 1: GitHub Codespaces (Recommended)
1. Push your code to GitHub
2. Create a Codespace with macOS
3. Install Xcode Command Line Tools
4. Run: `npx cap open ios`

### Option 2: Cloud Mac Service
1. Rent a Mac from MacStadium or AWS
2. Clone your repository
3. Install Xcode
4. Build and test iOS app

### Option 3: Local Mac Access
1. Borrow/rent a Mac temporarily
2. Install Xcode
3. Build for TestFlight

## Production Deployment Checklist

Before building for TestFlight:

### 1. Update Configuration
- [ ] Set production server URL in capacitor.config.ts
- [ ] Update Firebase OAuth redirect URIs
- [ ] Test authentication flows

### 2. App Store Assets
- [ ] Create proper PNG app icons (1024x1024 for App Store)
- [ ] Design launch screen/splash screen
- [ ] Write app description and keywords

### 3. iOS-Specific Requirements
- [ ] Add privacy manifest (required 2025)
- [ ] Configure app permissions (camera, photos, etc.)
- [ ] Set up Universal Links (optional)
- [ ] Add Sign in with Apple (if using other OAuth)

### 4. Testing
- [ ] Test on iOS Simulator
- [ ] Test on physical iPhone/iPad
- [ ] Verify all features work in WebView
- [ ] Test offline behavior

## Current Project Status

✅ **Phase 1 Complete**: Windows preparation finished
🔄 **Phase 2 Ready**: Cloud Mac setup for iOS build
⏳ **Phase 3 Pending**: TestFlight distribution

## Commands Reference

```bash
# Development
npm run dev                 # Start development server
npm run cap:copy           # Copy web assets to iOS
npm run cap:sync           # Sync and update iOS project
npm run icons:generate     # Regenerate app icons

# Testing
npm run health             # Check server health
npx cap doctor            # Check Capacitor setup

# Production (on macOS)
npx cap open ios          # Open Xcode project
npx cap build ios         # Build for iOS
```

## Troubleshooting

### Common Issues
1. **CORS errors**: Check server.js CORS configuration
2. **Firebase auth fails**: Verify redirect URIs include Capacitor origins
3. **Icons not loading**: Run `npm run icons:generate`
4. **Build errors**: Run `npm run cap:sync` to update

### Support Resources
- [Capacitor iOS Documentation](https://capacitorjs.com/docs/ios)
- [TestFlight Beta Testing](https://developer.apple.com/testflight/)
- [iOS Human Interface Guidelines](https://developer.apple.com/design/human-interface-guidelines/)

---

**Ready for Phase 2!** 🚀 Your Naroop project is fully prepared for iOS development.
