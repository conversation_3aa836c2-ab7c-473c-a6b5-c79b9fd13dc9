<?xml version="1.0" encoding="UTF-8"?>
<svg width="1024" height="1024" viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="appStoreGrad" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#f59e0b;stop-opacity:1" />
      <stop offset="25%" style="stop-color:#f97316;stop-opacity:1" />
      <stop offset="75%" style="stop-color:#d97706;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#b45309;stop-opacity:1" />
    </linearGradient>

    <linearGradient id="appStoreHighlight" x1="0%" y1="0%" x2="100%" y2="40%">
      <stop offset="0%" style="stop-color:#ffffff;stop-opacity:0.4" />
      <stop offset="100%" style="stop-color:#ffffff;stop-opacity:0" />
    </linearGradient>

    <filter id="appStoreShadow" x="-10%" y="-10%" width="120%" height="120%">
      <feDropShadow dx="0" dy="8" stdDeviation="12" flood-color="#000000" flood-opacity="0.25"/>
    </filter>
  </defs>

  <!-- Main background -->
  <rect width="1024" height="1024" rx="225.28" fill="url(#appStoreGrad)" filter="url(#appStoreShadow)"/>

  <!-- Highlight -->
  <rect width="1024" height="512" rx="225.28" fill="url(#appStoreHighlight)"/>

  <!-- Logo design -->
  <g transform="translate(512, 512)">
    <!-- Letter N with modern design -->
    <text x="0" y="20" dominant-baseline="middle" text-anchor="middle"
          font-family="SF Pro Display, -apple-system, Arial, sans-serif" font-weight="800"
          font-size="420" fill="#000000" opacity="0.15">N</text>
    <text x="0" y="0" dominant-baseline="middle" text-anchor="middle"
          font-family="SF Pro Display, -apple-system, Arial, sans-serif" font-weight="800"
          font-size="420" fill="white">N</text>
  </g>
</svg>