# Naroop CSS Architecture

This directory contains all the CSS files for the Naroop project, organized by functionality and purpose.

## Core Styles

### `variables.css`
- CSS custom properties (variables)
- Color palette, spacing, typography, and design tokens
- Theme definitions (light, dark, high-contrast)

### `main.css`
- Base styles and reset
- Core layout components
- Typography and button styles
- Global utilities

## Layout & Components

### `forms.css`
- Form styling and validation states
- Input, textarea, select styling
- Authentication modal styles
- Form buttons and interactions

### `posts.css`
- Social media post styling
- Post cards, headers, actions
- User avatars and metadata

### `notifications.css`
- Notification system styling
- Toast notifications
- Alert states (success, error, warning, info)

## Page-Specific Styles

### `index-specific.css`
### `index-specific.css`
- Main application page styles (legacy / in transition)
- (Prefer using `layout-unified.css` for structural layout going forward)

### `landing-specific.css`
- Landing page hero section
- Feature cards
- Call-to-action sections
- Authentication modals

## Responsive & Accessibility

### `responsive.css`
- Mobile-first responsive design
- Breakpoint-specific styles
- Touch device optimizations
- Print styles

### `preferences.css`
- User preference controls
- Theme switcher
- Font size controls
- Accessibility settings

## Performance & Animations

### `modern-animations.css`
- CSS animations and transitions
- Hover effects and micro-interactions
- Loading states and skeletons
- Reduced motion support

### `performance.css`
- Consolidated performance optimization styles
- Lazy loading indicators and shimmer effects
- Critical rendering path optimization
- GPU acceleration hints
- Mobile-specific optimizations and touch performance
- Battery-friendly animations and network-aware styles

## Specialized Features

### `scroll-sections.css`
- Scroll-snap functionality
- Section navigation
- Smooth scrolling effects
- Scroll indicators

## Consolidation Notes (2025)

- `layout-improvements.css` has been merged into `layout-unified.css` to provide a single source of truth for spacing, z-index layering, and responsive layout rules.
- Button styles were deduplicated inside `main.css` (single `.btn-base` definition). Adjust variants there only.
- When adding new layout utilities, append them to the bottom "Integrated Improvements" section of `layout-unified.css` with a clear comment block.

## Usage Guidelines

1. **Load Order**: Always load `variables.css` first, then `main.css`, then specific modules
2. **Responsive**: Use mobile-first approach with min-width media queries
3. **Performance**: Use CSS custom properties for consistent theming
4. **Accessibility**: Follow WCAG guidelines and support reduced motion preferences
5. **Naming**: Use BEM methodology for class naming consistency

## File Dependencies

```
variables.css (required first)
├── main.css (core styles)
├── responsive.css (responsive behavior)
├── forms.css (form components)
├── posts.css (post components)
├── notifications.css (notification system)
├── preferences.css (user preferences)
├── modern-animations.css (animations)
├── performance.css (consolidated performance optimizations)
├── scroll-sections.css (scroll features)
├── index-specific.css (main app page)
└── landing-specific.css (landing page)
```

## Browser Support

- Modern browsers (Chrome 90+, Firefox 88+, Safari 14+, Edge 90+)
- CSS Grid and Flexbox support required
- CSS Custom Properties support required
- Progressive enhancement for older browsers
