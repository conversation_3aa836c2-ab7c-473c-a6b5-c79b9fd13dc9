/* Glassmorphism & Modern Effects - 2025 */

/* Base Glassmorphism Classes */
.glass-card {
    background: var(--glass-background);
    backdrop-filter: var(--glass-blur);
    -webkit-backdrop-filter: var(--glass-blur);
    border: 1px solid var(--glass-border);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--glass-shadow);
    transition: all var(--transition-normal);
}

.glass-card:hover {
    background: rgba(255, 255, 255, 0.12);
    border-color: rgba(255, 255, 255, 0.15);
    transform: translateY(-2px);
    box-shadow: var(--glass-shadow), var(--shadow-glow);
}

.glass-light {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: var(--glass-blur-light);
    -webkit-backdrop-filter: var(--glass-blur-light);
    border: 1px solid rgba(255, 255, 255, 0.08);
    box-shadow: var(--glass-shadow-light);
}

/* Enhanced Card Designs */
.card-modern {
    background: var(--card-background);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-xl);
    box-shadow: var(--shadow-md);
    transition: all var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.card-modern::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: var(--gradient-card);
    opacity: 0.6;
}

.card-modern:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-lg);
    border-color: var(--primary-color);
}

.card-modern:hover::before {
    background: var(--gradient-primary);
    opacity: 1;
}

/* Header Glassmorphism */
.header-glass {
    background: rgba(13, 17, 23, 0.8);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border-bottom: 1px solid var(--glass-border);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
}

/* Enhanced Navigation */
.nav-item-modern {
    background: transparent;
    border: 1px solid transparent;
    border-radius: var(--border-radius-lg);
    transition: all var(--transition-normal);
    position: relative;
    overflow: hidden;
    padding: 12px 16px;
    margin: 4px 0;
    display: flex;
    align-items: center;
    gap: 12px;
    cursor: pointer;
}

.nav-item-modern::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: var(--gradient-primary);
    opacity: 0;
    transition: all var(--transition-normal);
    z-index: -1;
}

.nav-item-modern::after {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    width: 3px;
    height: 0;
    background: var(--primary-color);
    border-radius: 0 2px 2px 0;
    transition: all var(--transition-normal);
    transform: translateY(-50%);
}

.nav-item-modern:hover {
    background: rgba(255, 112, 67, 0.08);
    border-color: rgba(255, 112, 67, 0.2);
    transform: translateX(4px);
}

.nav-item-modern:hover::before {
    left: 0;
    opacity: 0.1;
}

.nav-item-modern:hover .nav-icon svg {
    transform: scale(1.1);
    stroke: var(--primary-color);
}

.nav-item-modern.active {
    background: var(--glass-background);
    border-color: var(--primary-color);
    box-shadow: var(--shadow-glow);
    transform: translateX(8px);
}

.nav-item-modern.active::before {
    left: 0;
    opacity: 0.15;
}

.nav-item-modern.active::after {
    height: 60%;
}

.nav-item-modern .nav-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 24px;
    height: 24px;
}

.nav-item-modern .nav-icon svg {
    transition: all var(--transition-normal);
    stroke: var(--text-secondary);
}

.nav-item-modern.active .nav-icon svg {
    stroke: var(--primary-color);
    transform: scale(1.1);
}

.nav-item-modern span {
    font-weight: var(--font-weight-medium);
    color: var(--text-secondary);
    transition: color var(--transition-normal);
}

.nav-item-modern.active span {
    color: var(--primary-color);
    font-weight: var(--font-weight-semibold);
}

/* Button Enhancements */
.btn-glass {
    background: var(--glass-background);
    backdrop-filter: var(--glass-blur-light);
    -webkit-backdrop-filter: var(--glass-blur-light);
    border: 1px solid var(--glass-border);
    color: var(--text-primary);
    transition: all var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.btn-glass::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: radial-gradient(circle, var(--primary-color) 0%, transparent 70%);
    opacity: 0;
    transition: all var(--transition-normal);
    transform: translate(-50%, -50%);
}

.btn-glass:hover {
    background: rgba(255, 255, 255, 0.15);
    border-color: var(--primary-color);
    transform: translateY(-2px);
    box-shadow: var(--shadow-glow);
}

.btn-glass:hover::before {
    width: 100px;
    height: 100px;
    opacity: 0.1;
}

/* Search Bar Modern */
.search-modern {
    background: var(--glass-background);
    backdrop-filter: var(--glass-blur-light);
    -webkit-backdrop-filter: var(--glass-blur-light);
    border: 1px solid var(--glass-border);
    border-radius: var(--border-radius-full);
    transition: all var(--transition-normal);
}

.search-modern:focus-within {
    background: rgba(255, 255, 255, 0.12);
    border-color: var(--accent-color);
    box-shadow: var(--shadow-glow-accent);
    transform: scale(1.02);
}

/* Floating Elements */
.floating-element {
    background: var(--glass-background);
    backdrop-filter: var(--glass-blur);
    -webkit-backdrop-filter: var(--glass-blur);
    border: 1px solid var(--glass-border);
    border-radius: var(--border-radius-xl);
    box-shadow: var(--glass-shadow);
    position: relative;
}

.floating-element::after {
    content: '';
    position: absolute;
    top: -1px;
    left: -1px;
    right: -1px;
    bottom: -1px;
    background: var(--gradient-primary);
    border-radius: var(--border-radius-xl);
    opacity: 0;
    z-index: -1;
    transition: opacity var(--transition-normal);
}

.floating-element:hover::after {
    opacity: 0.1;
}

/* Enhanced Micro-interactions */
.ripple-effect {
    position: relative;
    overflow: hidden;
    cursor: pointer;
}

.ripple {
    position: absolute;
    border-radius: 50%;
    background: radial-gradient(circle, rgba(255, 112, 67, 0.4) 0%, rgba(255, 112, 67, 0.1) 70%, transparent 100%);
    transform: scale(0);
    animation: ripple-animation 0.6s cubic-bezier(0.4, 0, 0.2, 1);
    pointer-events: none;
}

@keyframes ripple-animation {
    to {
        transform: scale(4);
        opacity: 0;
    }
}

/* Bounce Effect */
.bounce-on-click {
    animation: bounce-click 0.6s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

@keyframes bounce-click {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

/* Pulse Animation */
.pulse-glow {
    animation: pulse-glow 2s infinite;
}

@keyframes pulse-glow {
    0%, 100% {
        box-shadow: var(--shadow-md);
        transform: scale(1);
    }
    50% {
        box-shadow: var(--shadow-glow);
        transform: scale(1.02);
    }
}

/* Glow Effects */
.glow-primary {
    box-shadow: var(--shadow-glow);
}

.glow-accent {
    box-shadow: var(--shadow-glow-accent);
}

/* Smooth Animations */
.smooth-hover {
    transition: all var(--transition-normal);
}

.smooth-hover:hover {
    transform: translateY(-2px) scale(1.02);
}

/* Loading Shimmer Effect */
.shimmer {
    background: linear-gradient(
        90deg,
        var(--card-background) 25%,
        rgba(255, 255, 255, 0.1) 50%,
        var(--card-background) 75%
    );
    background-size: 200% 100%;
    animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
    0% {
        background-position: -200% 0;
    }
    100% {
        background-position: 200% 0;
    }
}

/* Enhanced Mobile Navigation */
.mobile-nav-item {
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 8px 12px;
    border-radius: var(--border-radius-lg);
    transition: all var(--transition-normal);
    text-decoration: none;
    color: var(--text-secondary);
    overflow: hidden;
}

.mobile-nav-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 50%;
    width: 0;
    height: 2px;
    background: var(--primary-color);
    transition: all var(--transition-normal);
    transform: translateX(-50%);
}

.mobile-nav-item:hover {
    background: rgba(255, 112, 67, 0.08);
    transform: translateY(-2px);
}

.mobile-nav-item.active {
    color: var(--primary-color);
    background: rgba(255, 112, 67, 0.12);
}

.mobile-nav-item.active::before {
    width: 80%;
}

.mobile-nav-item .mobile-nav-icon {
    margin-bottom: 4px;
    transition: all var(--transition-normal);
}

.mobile-nav-item .mobile-nav-icon svg {
    transition: all var(--transition-normal);
    stroke: currentColor;
}

.mobile-nav-item:hover .mobile-nav-icon svg {
    transform: scale(1.1);
}

.mobile-nav-item.active .mobile-nav-icon svg {
    transform: scale(1.15);
    stroke: var(--primary-color);
}

.mobile-nav-item span {
    font-size: var(--font-size-xs);
    font-weight: var(--font-weight-medium);
    transition: all var(--transition-normal);
}

.mobile-nav-item.active span {
    font-weight: var(--font-weight-semibold);
    color: var(--primary-color);
}

/* Notification Badge */
.notification-badge {
    position: absolute;
    top: -2px;
    right: -2px;
    width: 8px;
    height: 8px;
    background: var(--error-color);
    border-radius: 50%;
    border: 2px solid var(--background-color);
    animation: pulse-notification 2s infinite;
}

@keyframes pulse-notification {
    0%, 100% {
        transform: scale(1);
        opacity: 1;
    }
    50% {
        transform: scale(1.2);
        opacity: 0.8;
    }
}

/* Story Highlights Enhancements */
.story-item {
    transition: all var(--transition-normal);
}

.story-item:hover {
    transform: translateY(-4px);
}

.story-item:hover .story-avatar {
    transform: scale(1.05);
    box-shadow: var(--shadow-glow);
}

.story-avatar {
    transition: all var(--transition-normal);
    position: relative;
}

.story-avatar::before {
    content: '';
    position: absolute;
    top: -3px;
    left: -3px;
    right: -3px;
    bottom: -3px;
    border-radius: 50%;
    background: conic-gradient(from 0deg, var(--primary-color), var(--secondary-color), var(--accent-color), var(--primary-color));
    z-index: -1;
    opacity: 0;
    transition: opacity var(--transition-normal);
}

.story-item:hover .story-avatar::before {
    opacity: 1;
    animation: rotate-gradient 2s linear infinite;
}

@keyframes rotate-gradient {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.add-story .story-avatar {
    background: var(--gradient-primary);
    box-shadow: var(--shadow-md);
}

.add-story:hover .story-avatar {
    box-shadow: var(--shadow-glow);
    background: linear-gradient(135deg, var(--primary-light), var(--secondary-light));
}
