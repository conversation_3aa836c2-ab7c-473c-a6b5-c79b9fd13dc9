/* Naroop Layout Improvements - 2025 Modern Standards */

/* ===== CONTAINER SPACING SYSTEM ===== */

/* Feed Content Container - Proper spacing */
.feed-content {
    display: grid;
    gap: var(--gap-card);
    padding: var(--spacing-lg);
}

/* Content Sections - Consistent spacing */
.content-section {
    display: grid;
    gap: var(--gap-component);
    margin-bottom: var(--gap-section);
}

/* ===== CARD LAYOUT IMPROVEMENTS ===== */

/* Universal card container spacing */
.cards-container,
.posts-container,
#postsContainer {
    display: grid;
    gap: var(--gap-card);
    width: 100%;
}

/* Feature cards grid - Landing page */
.feature-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: var(--gap-section);
    margin-top: var(--gap-section);
}

/* ===== Z-INDEX MANAGEMENT ===== */

/* Header and navigation */
.header {
    z-index: var(--z-fixed);
}

.mobile-nav {
    z-index: var(--z-fixed);
}

/* Sidebar and trending */
.sidebar {
    z-index: var(--z-sticky);
}

.trending {
    z-index: var(--z-sticky);
}

/* Modals and overlays */
.modal-backdrop {
    z-index: var(--z-modal-backdrop);
}

.modal,
.auth-modal {
    z-index: var(--z-modal);
}

/* Dropdowns and popovers */
.dropdown-menu,
.user-menu {
    z-index: var(--z-dropdown);
}

/* ===== PREVENT LAYOUT SHIFTS ===== */

/* Ensure hover effects don't cause shifts */
.feed-card,
.card,
.nav-item {
    transform-origin: center;
    will-change: transform;
}

/* Smooth transitions without layout impact */
.feed-card:hover,
.card:hover {
    transform: translateY(-4px);
    /* Use transform instead of margin/padding changes */
}

/* ===== RESPONSIVE SPACING IMPROVEMENTS ===== */

/* Extra small devices - Optimized touch targets */
@media (max-width: 575.98px) {
    .cards-container,
    #postsContainer {
        gap: var(--spacing-md);
    }
    
    .feed-content {
        padding: var(--spacing-md);
        gap: var(--spacing-md);
    }
    
    /* Ensure minimum touch target size (44px) */
    .feed-action-btn,
    .nav-item,
    .mobile-nav-item {
        min-height: 44px;
        min-width: 44px;
    }
}

/* Small devices - Balanced spacing */
@media (min-width: 576px) and (max-width: 767.98px) {
    .cards-container,
    #postsContainer {
        gap: var(--gap-card);
    }
    
    .feed-content {
        padding: var(--spacing-lg);
    }
}

/* Medium devices and up - Full spacing */
@media (min-width: 768px) {
    .cards-container,
    #postsContainer {
        gap: var(--gap-card);
    }
    
    .main-content {
        gap: var(--gap-section);
    }
}

/* ===== CONTENT OVERFLOW HANDLING ===== */

/* Prevent horizontal overflow */
.feed-card-content,
.post-content {
    overflow-wrap: break-word;
    word-wrap: break-word;
    hyphens: auto;
}

/* Long content handling */
.feed-card-content p,
.post-content p {
    max-width: 100%;
    overflow: hidden;
}

/* ===== ACCESSIBILITY IMPROVEMENTS ===== */

/* Focus management for overlapping prevention */
.focus-ring:focus-visible {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
    z-index: var(--z-dropdown);
}

/* Ensure interactive elements don't overlap */
.interactive-element {
    position: relative;
    z-index: var(--z-base);
}

.interactive-element:focus,
.interactive-element:hover {
    z-index: var(--z-dropdown);
}

/* ===== MODERN CSS FEATURES ===== */

/* Container queries for responsive cards (when supported) */
@supports (container-type: inline-size) {
    .cards-container {
        container-type: inline-size;
    }
    
    @container (max-width: 400px) {
        .feed-card {
            padding: var(--spacing-md);
        }
    }
}

/* Logical properties for better internationalization */
.card-padding {
    padding-block: var(--spacing-lg);
    padding-inline: var(--spacing-lg);
}

.card-margin {
    margin-block-end: var(--gap-card);
}

/* ===== MODERN SOCIAL MEDIA SCROLL BEHAVIOR ===== */

/* Main content scroll container - Instagram/Twitter style */
.main-content {
    scroll-behavior: smooth;
    overflow-y: auto;
    height: calc(100vh - var(--header-height));
}

/* Feed sections - Proper separation and scroll behavior */
.content-section {
    scroll-margin-top: calc(var(--header-height) + var(--spacing-lg));
    position: relative;
    z-index: var(--z-base);
    background: var(--background-color);
    border-radius: var(--border-radius-lg);
    margin-bottom: var(--gap-section);
    overflow: hidden;
}

/* Feed content - Infinite scroll ready */
.feed-content {
    position: relative;
    z-index: var(--z-base);
    background: var(--background-color);
}

/* Stories container styles moved to stories-fix.css */

/* Story prompt - Proper positioning */
.story-prompt {
    position: relative;
    z-index: var(--z-base);
    background: var(--gradient-card);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-lg);
    margin-bottom: var(--gap-card);
    text-align: center;
}

/* Prevent scroll-triggered layout shifts */
.sticky-element {
    contain: layout style paint;
}

/* Sticky sidebar and trending - Proper scroll behavior */
.sidebar,
.trending {
    position: sticky;
    top: calc(var(--header-height) + var(--spacing-lg));
    height: calc(100vh - var(--header-height) - var(--spacing-xl));
    overflow-y: auto;
    scrollbar-width: thin;
    scrollbar-color: var(--border-color) transparent;
}

.sidebar::-webkit-scrollbar,
.trending::-webkit-scrollbar {
    width: 6px;
}

.sidebar::-webkit-scrollbar-track,
.trending::-webkit-scrollbar-track {
    background: transparent;
}

.sidebar::-webkit-scrollbar-thumb,
.trending::-webkit-scrollbar-thumb {
    background: var(--border-color);
    border-radius: 3px;
}

/* Feed header - Non-sticky for better scroll experience */
.feed-header {
    position: relative;
    z-index: var(--z-base);
    background: var(--surface-color);
    border-bottom: 1px solid var(--divider-color);
    border-radius: var(--border-radius-lg) var(--border-radius-lg) 0 0;
}

/* ===== INFINITE SCROLL & LOAD MORE ===== */

/* Load more button - Instagram style */
.load-more-container {
    display: flex;
    justify-content: center;
    padding: var(--gap-card) 0;
    margin-top: var(--gap-card);
}

.load-more-btn {
    background: var(--card-background);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-md) var(--spacing-xl);
    color: var(--text-primary);
    font-weight: var(--font-weight-medium);
    cursor: pointer;
    transition: all var(--transition-normal);
}

.load-more-btn:hover {
    background: var(--surface-color);
    border-color: var(--primary-color);
    transform: translateY(-2px);
}

/* Loading indicator for infinite scroll */
.loading-indicator {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: var(--gap-card);
    color: var(--text-muted);
}

.loading-spinner {
    width: 24px;
    height: 24px;
    border: 2px solid var(--border-color);
    border-top: 2px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* ===== SECTION SEPARATION ===== */

/* Clear section boundaries - Twitter/Instagram style */
.content-section + .content-section {
    border-top: 8px solid var(--background-color);
    margin-top: 0;
}

/* Section headers - Sticky when needed */
.section-header {
    position: sticky;
    top: var(--header-height);
    background: var(--surface-color);
    border-bottom: 1px solid var(--divider-color);
    padding: var(--spacing-md) var(--spacing-lg);
    z-index: var(--z-sticky);
    backdrop-filter: blur(10px);
}

/* ===== GRID FALLBACKS ===== */

/* Flexbox fallback for older browsers */
@supports not (display: grid) {
    .cards-container,
    #postsContainer {
        display: flex;
        flex-direction: column;
    }

    .feed-card,
    .post {
        margin-bottom: var(--gap-card);
    }

    .feed-card:last-child,
    .post:last-child {
        margin-bottom: 0;
    }
}
