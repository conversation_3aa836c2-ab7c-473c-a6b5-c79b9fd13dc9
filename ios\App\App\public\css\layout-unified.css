/* Naroop Unified Layout System - 2025 Modern Standards */

/* ===== MOBILE-FIRST RESPONSIVE LAYOUT ===== */

/* Main content container - Simple and reliable */
.main-content {
    display: grid;
    grid-template-columns: 1fr;
    min-height: calc(100vh - var(--header-height));
    gap: var(--spacing-lg);
    padding: var(--spacing-lg);
    max-width: var(--container-max-width);
    margin: 0 auto;
}

/* Mobile navigation */
.mobile-nav {
    grid-area: mobile-nav;
    position: sticky;
    bottom: 0;
    background: var(--surface-color);
    border-top: 1px solid var(--border-color);
    padding: var(--spacing-sm) var(--spacing-md);
    z-index: var(--z-sticky);
    backdrop-filter: blur(10px);
    display: flex;
    justify-content: space-around;
    align-items: center;
    height: var(--mobile-nav-height);
}

/* Mobile nav items */
.mobile-nav-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-xs);
    border-radius: var(--border-radius-lg);
    transition: all var(--transition-fast);
    min-width: 44px;
    min-height: 44px;
    justify-content: center;
    text-decoration: none;
    color: var(--text-secondary);
    position: relative;
    overflow: hidden;
    font-weight: var(--font-weight-medium);
}

.mobile-nav-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 50%;
    width: 0;
    height: 3px;
    background: var(--primary-color);
    transition: all var(--transition-fast);
    transform: translateX(-50%);
    border-radius: 0 0 var(--border-radius-sm) var(--border-radius-sm);
}

.mobile-nav-item:hover {
    background: var(--surface-color);
    color: var(--text-primary);
    transform: translateY(-2px);
}

.mobile-nav-item:hover::before {
    width: 20px;
}

.mobile-nav-item.active {
    background: var(--primary-color);
    color: white;
    transform: translateY(-4px);
    box-shadow: 0 8px 25px rgba(255, 112, 67, 0.3);
}

.mobile-nav-item.active::before {
    width: 30px;
    background: white;
}

.mobile-nav-item svg {
    width: 20px;
    height: 20px;
    transition: transform var(--transition-fast);
}

.mobile-nav-item:hover svg {
    transform: scale(1.1);
}

.mobile-nav-item.active svg {
    transform: scale(1.2);
}

.mobile-nav-item span {
    font-size: var(--font-size-xs);
    font-weight: var(--font-weight-medium);
    transition: all var(--transition-fast);
}

.mobile-nav-item.active span {
    font-weight: var(--font-weight-semibold);
}

/* Mobile navigation badges */
.mobile-nav-item .badge {
    position: absolute;
    top: 4px;
    right: 8px;
    background: var(--accent-color);
    color: white;
    font-size: 10px;
    font-weight: var(--font-weight-bold);
    padding: 1px 4px;
    border-radius: var(--border-radius-full);
    min-width: 14px;
    height: 14px;
    display: flex;
    align-items: center;
    justify-content: center;
    animation: bounce 1s infinite;
}

@keyframes bounce {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.2); }
}

/* Desktop sidebar - Hidden on mobile */
.sidebar {
    display: none;
}

/* Trending sidebar - Hidden on mobile */
.trending {
    display: none;
}

/* ===== TABLET LAYOUT (768px+) ===== */
@media (min-width: 768px) {
    .main-content {
        grid-template-columns: 250px 1fr;
        gap: var(--spacing-xl);
    }

    .sidebar {
        display: block;
        background: var(--card-background);
        border: 1px solid var(--border-color);
        border-radius: var(--border-radius-lg);
        padding: var(--spacing-lg);
        height: fit-content;
        position: sticky;
        top: calc(var(--header-height) + var(--spacing-lg));
        max-height: calc(100vh - var(--header-height) - var(--spacing-xl));
        overflow-y: auto;
    }

    .mobile-nav {
        display: none;
    }
}

/* ===== DESKTOP LAYOUT (1024px+) ===== */
@media (min-width: 1024px) {
    .main-content {
        grid-template-columns: var(--sidebar-width) 1fr 300px;
        gap: var(--spacing-xl);
    }

    .trending {
        display: block;
        background: var(--card-background);
        border: 1px solid var(--border-color);
        border-radius: var(--border-radius-lg);
        padding: var(--spacing-lg);
        height: fit-content;
        position: sticky;
        top: calc(var(--header-height) + var(--spacing-lg));
        max-height: calc(100vh - var(--header-height) - var(--spacing-xl));
        overflow-y: auto;
    }
}

/* ===== CONTENT SECTIONS ===== */

/* Feed content area */
.feed {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
    width: 100%;
}

/* Content sections - Modern card-based layout with distinct styles */
.content-section {
    background: var(--card-background);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-lg);
    overflow: hidden;
    position: relative;
    z-index: var(--z-base);
    transition: all var(--transition-normal);
    margin-bottom: var(--gap-card);
}

.content-section:hover {
    border-color: var(--border-color-hover, var(--border-color));
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* Feed Section - Primary accent */
#feed-section .content-section {
    border-left: 4px solid var(--primary-color);
}

#feed-section .feed-header {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
    color: white;
}

#feed-section .feed-title::before {
    content: '🏠';
    margin-right: var(--spacing-sm);
}

/* Explore Section - Secondary accent */
#explore-section {
    border-left: 4px solid var(--secondary-color);
}

#explore-section .feed-header {
    background: linear-gradient(135deg, var(--secondary-color), var(--secondary-light));
    color: var(--text-primary);
}

#explore-section .feed-title::before {
    content: '🔍';
    margin-right: var(--spacing-sm);
}

/* Messages Section - Accent color */
#messages-section {
    border-left: 4px solid var(--accent-color);
}

#messages-section .feed-header {
    background: linear-gradient(135deg, var(--accent-color), var(--accent-light));
    color: white;
}

#messages-section .feed-title::before {
    content: '💬';
    margin-right: var(--spacing-sm);
}

/* Profile Section - Success color */
#profile-section {
    border-left: 4px solid #10B981;
}

#profile-section .feed-header {
    background: linear-gradient(135deg, #10B981, #34D399);
    color: white;
}

#profile-section .feed-title::before {
    content: '👤';
    margin-right: var(--spacing-sm);
}

/* Communities Section - Purple accent */
#communities-section {
    border-left: 4px solid #8B5CF6;
}

#communities-section .feed-header {
    background: linear-gradient(135deg, #8B5CF6, #A78BFA);
    color: white;
}

#communities-section .feed-title::before {
    content: '👥';
    margin-right: var(--spacing-sm);
}

/* Analytics Section - Warning color */
#analytics-section {
    border-left: 4px solid #F59E0B;
}

#analytics-section .feed-header {
    background: linear-gradient(135deg, #F59E0B, #FBBF24);
    color: white;
}

#analytics-section .feed-title::before {
    content: '📊';
    margin-right: var(--spacing-sm);
}

/* Section headers */
.feed-header {
    background: var(--surface-color);
    border-bottom: 1px solid var(--divider-color);
    padding: var(--spacing-lg);
    position: relative;
    z-index: var(--z-base);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.feed-title {
    margin: 0;
    font-size: var(--font-size-xl);
    font-weight: var(--font-weight-semibold);
    color: var(--text-primary);
    display: flex;
    align-items: center;
}

/* Section content */
.feed-content {
    padding: var(--spacing-lg);
    position: relative;
    z-index: var(--z-base);
}

/* Section action buttons */
.section-action-btn {
    background: rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: white;
    padding: var(--spacing-xs) var(--spacing-md);
    border-radius: var(--border-radius-md);
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
    cursor: pointer;
    transition: all var(--transition-fast);
}

.section-action-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-1px);
}

/* ===== CARDS & POSTS LAYOUT ===== */

/* Posts container - CSS Grid for consistent spacing */
.posts-container,
#postsContainer {
    display: grid;
    gap: var(--gap-card);
    width: 100%;
}

/* Individual post cards - Enhanced visual hierarchy */
.feed-card,
.post-card {
    background: var(--card-background);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-lg);
    padding: 0;
    transition: all var(--transition-normal);
    position: relative;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.feed-card:hover,
.post-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
    border-color: var(--primary-color);
}

/* Post card header */
.feed-card-header {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--divider-color);
    background: var(--surface-color);
}

.feed-card-avatar {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    background: var(--primary-color);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-bold);
    color: white;
}

.feed-card-user-info {
    flex: 1;
}

.feed-card-username {
    margin: 0 0 var(--spacing-xs) 0;
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-semibold);
    color: var(--text-primary);
}

.feed-card-timestamp {
    margin: 0;
    font-size: var(--font-size-sm);
    color: var(--text-muted);
}

/* Post card content */
.feed-card-content {
    padding: var(--spacing-lg);
    line-height: var(--line-height-relaxed);
}

.feed-card-content p {
    margin: 0;
    color: var(--text-secondary);
    font-size: var(--font-size-base);
}

/* Post card actions */
.feed-card-actions {
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);
    padding: var(--spacing-md) var(--spacing-lg);
    border-top: 1px solid var(--divider-color);
    background: var(--surface-color);
}

.feed-action-btn {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    background: none;
    border: none;
    color: var(--text-muted);
    font-size: var(--font-size-sm);
    cursor: pointer;
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--border-radius-md);
    transition: all var(--transition-fast);
    min-height: 32px;
}

.feed-action-btn:hover {
    background: var(--surface-color);
    color: var(--text-primary);
    transform: translateY(-1px);
}

.feed-action-btn.liked {
    color: #EF4444;
}

.feed-action-btn.liked:hover {
    background: rgba(239, 68, 68, 0.1);
}

/* Create post container */
.create-post-container {
    background: var(--surface-color);
    border: 2px dashed var(--border-color);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-xl);
    text-align: center;
    transition: all var(--transition-normal);
    cursor: pointer;
}

.create-post-container:hover {
    border-color: var(--primary-color);
    background: var(--card-background);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(255, 112, 67, 0.1);
}

.create-post-container p {
    margin: 0 0 var(--spacing-lg) 0;
    color: var(--text-secondary);
    font-size: var(--font-size-lg);
}

/* Empty states */
.empty-state {
    text-align: center;
    padding: var(--spacing-2xl);
    color: var(--text-muted);
}

.empty-state h3,
.empty-state h4 {
    color: var(--text-secondary);
    margin-bottom: var(--spacing-md);
}

.empty-state p {
    margin: 0;
    line-height: var(--line-height-relaxed);
}

.empty-state-icon {
    font-size: 3rem;
    margin-bottom: var(--spacing-lg);
    opacity: 0.5;
}

/* Category tags */
.category-tag {
    display: inline-block;
    background: var(--primary-color);
    color: white;
    padding: var(--spacing-xs) var(--spacing-md);
    border-radius: var(--border-radius-full);
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
    margin: var(--spacing-xs);
    transition: all var(--transition-fast);
    cursor: pointer;
}

.category-tag:hover {
    background: var(--primary-light);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(255, 112, 67, 0.3);
}

.explore-categories {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-sm);
    margin-top: var(--spacing-lg);
}

/* ===== STORIES SECTION ===== */

/* Stories section - Modern Instagram-like layout */
.stories-section {
    background: var(--card-background);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-lg);
    margin-bottom: var(--gap-card);
    overflow: hidden;
    position: relative;
    z-index: var(--z-base);
    transition: all var(--transition-normal);
}

.stories-section:hover {
    border-color: var(--primary-color);
    box-shadow: 0 4px 20px rgba(255, 112, 67, 0.1);
}

.stories-section .stories-container {
    display: flex;
    gap: var(--spacing-lg);
    overflow-x: auto;
    padding: var(--spacing-sm) 0;
    scrollbar-width: none;
    -ms-overflow-style: none;
    scroll-behavior: smooth;
}

.stories-section .stories-container::-webkit-scrollbar {
    display: none;
}

/* Story items - Enhanced Instagram-style */
.stories-section .story-item {
    flex-shrink: 0;
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background: linear-gradient(45deg, var(--primary-color), var(--secondary-color), var(--accent-color));
    padding: 3px;
    cursor: pointer;
    transition: all var(--transition-normal);
    position: relative;
    animation: storyRingRotate 3s linear infinite paused;
}

.stories-section .story-item:hover {
    transform: scale(1.1);
    animation-play-state: running;
    box-shadow: 0 8px 25px rgba(255, 112, 67, 0.4);
}

.stories-section .story-item:active {
    transform: scale(1.05);
}

/* Story ring animation */
@keyframes storyRingRotate {
    0% { transform: rotate(0deg) scale(1); }
    25% { transform: rotate(90deg) scale(1.05); }
    50% { transform: rotate(180deg) scale(1); }
    75% { transform: rotate(270deg) scale(1.05); }
    100% { transform: rotate(360deg) scale(1); }
}

/* Story avatar */
.stories-section .story-avatar {
    width: 100%;
    height: 100%;
    border-radius: 50%;
    background: var(--surface-color);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size-xl);
    border: 3px solid var(--card-background);
    transition: all var(--transition-fast);
    position: relative;
    overflow: hidden;
}

.stories-section .story-item:hover .story-avatar {
    border-color: white;
    transform: scale(0.95);
}

/* Add story button special styling */
.stories-section .story-item:first-child {
    background: var(--border-color);
    position: relative;
}

.stories-section .story-item:first-child::after {
    content: '';
    position: absolute;
    bottom: 5px;
    right: 5px;
    width: 24px;
    height: 24px;
    background: var(--primary-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 2px solid var(--card-background);
    z-index: 1;
}

.stories-section .story-item:first-child .story-avatar {
    background: var(--surface-color);
    color: var(--text-secondary);
}

.stories-section .story-item:first-child:hover {
    background: var(--primary-color);
}

.stories-section .story-item:first-child:hover .story-avatar {
    color: var(--text-primary);
}

/* Story status indicators */
.stories-section .story-item.has-new-story {
    background: linear-gradient(45deg, #ff6b6b, #feca57, #48dbfb);
    animation: storyPulse 2s ease-in-out infinite;
}

.stories-section .story-item.viewed-story {
    background: var(--border-color);
    opacity: 0.7;
}

@keyframes storyPulse {
    0%, 100% {
        box-shadow: 0 0 0 0 rgba(255, 112, 67, 0.7);
    }
    50% {
        box-shadow: 0 0 0 10px rgba(255, 112, 67, 0);
    }
}

/* Story preview on hover */
.stories-section .story-item::before {
    content: '';
    position: absolute;
    top: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 0;
    height: 0;
    border-left: 8px solid transparent;
    border-right: 8px solid transparent;
    border-bottom: 8px solid var(--surface-color);
    opacity: 0;
    transition: opacity var(--transition-fast);
    z-index: 2;
}

.stories-section .story-item:hover::before {
    opacity: 1;
}

/* Story names/labels */
.stories-section .story-name {
    position: absolute;
    bottom: -25px;
    left: 50%;
    transform: translateX(-50%);
    font-size: var(--font-size-xs);
    color: var(--text-secondary);
    white-space: nowrap;
    opacity: 0;
    transition: opacity var(--transition-fast);
    pointer-events: none;
}

.stories-section .story-item:hover .story-name {
    opacity: 1;
}

/* ===== STORY VIEWER MODAL ===== */

.story-viewer {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: var(--z-modal);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    visibility: hidden;
    transition: all var(--transition-normal);
}

.story-viewer.active {
    opacity: 1;
    visibility: visible;
}

.story-viewer-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.9);
    backdrop-filter: blur(10px);
}

.story-viewer-content {
    position: relative;
    width: 400px;
    height: 700px;
    background: var(--card-background);
    border-radius: var(--border-radius-xl);
    overflow: hidden;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
    transform: scale(0.8);
    transition: transform var(--transition-normal);
}

.story-viewer.active .story-viewer-content {
    transform: scale(1);
}

.story-viewer-header {
    position: relative;
    padding: var(--spacing-lg);
    background: linear-gradient(180deg, rgba(0,0,0,0.8) 0%, transparent 100%);
    color: white;
    z-index: 2;
}

.story-progress-bars {
    display: flex;
    gap: 4px;
    margin-bottom: var(--spacing-md);
}

.story-progress-bar {
    flex: 1;
    height: 3px;
    background: rgba(255, 255, 255, 0.3);
    border-radius: var(--border-radius-full);
    overflow: hidden;
}

.story-progress-bar.completed {
    background: white;
}

.story-progress-bar.active {
    background: rgba(255, 255, 255, 0.3);
    position: relative;
}

.story-progress-bar.active::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    background: white;
    border-radius: var(--border-radius-full);
    animation: storyProgress 5s linear forwards;
}

@keyframes storyProgress {
    from { width: 0%; }
    to { width: 100%; }
}

.story-user-info {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.story-user-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: var(--primary-color);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size-lg);
}

.story-user-details {
    display: flex;
    flex-direction: column;
}

.story-username {
    font-weight: var(--font-weight-semibold);
    font-size: var(--font-size-base);
}

.story-timestamp {
    font-size: var(--font-size-sm);
    opacity: 0.8;
}

.story-close-btn {
    position: absolute;
    top: var(--spacing-lg);
    right: var(--spacing-lg);
    background: none;
    border: none;
    color: white;
    cursor: pointer;
    padding: var(--spacing-xs);
    border-radius: 50%;
    transition: background var(--transition-fast);
}

.story-close-btn:hover {
    background: rgba(255, 255, 255, 0.2);
}

.story-viewer-media {
    position: relative;
    height: calc(100% - 140px);
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--surface-color);
}

.story-content {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-xl);
    position: relative;
}

.story-text-content {
    text-align: center;
    color: var(--text-primary);
}

.story-text-content p {
    font-size: var(--font-size-xl);
    line-height: var(--line-height-relaxed);
    margin: 0;
}

.story-navigation {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    width: 100%;
    display: flex;
    justify-content: space-between;
    padding: 0 var(--spacing-lg);
    pointer-events: none;
}

.story-nav-btn {
    background: rgba(0, 0, 0, 0.5);
    border: none;
    color: white;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all var(--transition-fast);
    pointer-events: all;
}

.story-nav-btn:hover {
    background: rgba(0, 0, 0, 0.8);
    transform: scale(1.1);
}

.story-viewer-actions {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    padding: var(--spacing-lg);
    background: linear-gradient(0deg, rgba(0,0,0,0.8) 0%, transparent 100%);
}

.story-reaction-bar {
    display: flex;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-md);
    justify-content: center;
}

.story-reaction-btn {
    background: none;
    border: none;
    font-size: var(--font-size-xl);
    cursor: pointer;
    padding: var(--spacing-xs);
    border-radius: 50%;
    transition: all var(--transition-fast);
}

.story-reaction-btn:hover {
    transform: scale(1.2);
    background: rgba(255, 255, 255, 0.1);
}

.story-input-area {
    display: flex;
    gap: var(--spacing-sm);
    align-items: center;
}

.story-reply-input {
    flex: 1;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: var(--border-radius-full);
    padding: var(--spacing-sm) var(--spacing-md);
    color: white;
    font-size: var(--font-size-base);
}

.story-reply-input::placeholder {
    color: rgba(255, 255, 255, 0.6);
}

.story-send-btn {
    background: var(--primary-color);
    border: none;
    color: white;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all var(--transition-fast);
}

.story-send-btn:hover {
    background: var(--primary-light);
    transform: scale(1.1);
}

/* Reaction animation */
@keyframes reactionFloat {
    0% {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
    100% {
        opacity: 0;
        transform: translateY(-100px) scale(1.5);
    }
}

/* Mobile story viewer adjustments */
@media (max-width: 767.98px) {
    .story-viewer-content {
        width: 100%;
        height: 100%;
        border-radius: 0;
    }
}

/* ===== MODERN INTERACTION ANIMATIONS ===== */

/* Ripple effect */
@keyframes ripple {
    to {
        transform: scale(4);
        opacity: 0;
    }
}

/* Heart animations */
@keyframes heartBeat {
    0%, 100% { transform: scale(1); }
    25% { transform: scale(1.2); }
    50% { transform: scale(1.1); }
    75% { transform: scale(1.25); }
}

@keyframes heartShrink {
    0% { transform: scale(1); }
    100% { transform: scale(0.8); }
}

@keyframes floatHeart {
    0% {
        opacity: 1;
        transform: translateY(0) rotate(0deg);
    }
    100% {
        opacity: 0;
        transform: translateY(-100px) rotate(180deg);
    }
}

@keyframes bigHeartPop {
    0% {
        opacity: 0;
        transform: translate(-50%, -50%) scale(0);
    }
    50% {
        opacity: 1;
        transform: translate(-50%, -50%) scale(1.2);
    }
    100% {
        opacity: 0;
        transform: translate(-50%, -50%) scale(1);
    }
}

/* Toast animations */
@keyframes toastSlideUp {
    from {
        opacity: 0;
        transform: translateX(-50%) translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateX(-50%) translateY(0);
    }
}

@keyframes toastSlideDown {
    from {
        opacity: 1;
        transform: translateX(-50%) translateY(0);
    }
    to {
        opacity: 0;
        transform: translateX(-50%) translateY(20px);
    }
}

/* Card hover states */
.feed-card.hovered {
    transform: translateY(-6px);
    box-shadow: 0 16px 50px rgba(0, 0, 0, 0.2);
}

/* Fade in animation for new posts */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in-up {
    animation: fadeInUp 0.6s ease-out;
}

/* Loading skeleton improvements */
.loading-skeleton {
    background: linear-gradient(90deg, var(--card-background) 25%, var(--surface-color) 50%, var(--card-background) 75%);
    background-size: 200% 100%;
    animation: shimmer 1.5s infinite;
    border-radius: var(--border-radius-lg);
    height: 200px;
    margin-bottom: var(--gap-card);
}

@keyframes shimmer {
    0% { background-position: -200% 0; }
    100% { background-position: 200% 0; }
}

/* Enhanced button interactions */
.btn-base:active {
    transform: translateY(1px) scale(0.98);
}

.feed-action-btn:active {
    transform: scale(0.95);
}

/* Liked state styling */
.feed-action-btn.liked {
    color: #EF4444;
    background: rgba(239, 68, 68, 0.1);
}

.feed-action-btn.liked svg {
    fill: #EF4444;
}

/* Pull to refresh styling */
.pull-to-refresh {
    position: absolute;
    top: -60px;
    left: 50%;
    transform: translateX(-50%);
    background: var(--primary-color);
    color: white;
    padding: var(--spacing-sm) var(--spacing-lg);
    border-radius: var(--border-radius-full);
    font-size: var(--font-size-sm);
    opacity: 0;
    transition: all var(--transition-normal);
    z-index: var(--z-sticky);
}

.pull-to-refresh.active {
    opacity: 1;
    top: var(--spacing-lg);
}

/* Smooth scrolling improvements */
.content-area {
    scroll-behavior: smooth;
}

/* Focus improvements for accessibility */
.feed-card:focus-within {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

.feed-action-btn:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
    background: var(--surface-color);
}

/* Touch improvements */
@media (hover: none) and (pointer: coarse) {
    .feed-card:hover {
        transform: none;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    }

    .feed-card:active {
        transform: scale(0.98);
    }

    .nav-item:hover,
    .mobile-nav-item:hover {
        transform: none;
    }

    .nav-item:active,
    .mobile-nav-item:active {
        transform: scale(0.95);
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }

    .story-item {
        animation: none !important;
    }

    .loading-skeleton {
        animation: none !important;
        background: var(--surface-color);
    }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .feed-card {
        border-width: 2px;
    }

    .feed-action-btn {
        border: 1px solid currentColor;
    }

    .category-tag {
        border: 2px solid currentColor;
    }
}

/* ===== PERFORMANCE OPTIMIZATIONS ===== */

/* GPU acceleration for smooth animations */
.feed-card,
.story-item,
.nav-item,
.mobile-nav-item,
.btn-base {
    will-change: transform;
    transform: translateZ(0);
}

/* Optimize repaints */
.stories-container,
.posts-container {
    contain: layout style paint;
}

/* Optimize scrolling performance */
.content-area,
.sidebar,
.trending {
    contain: strict;
    will-change: scroll-position;
}

/* Optimize animations for 60fps */
.feed-card:hover,
.story-item:hover,
.nav-item:hover,
.mobile-nav-item:hover {
    transform: translateZ(0) translateY(-4px);
}

/* Optimize image loading */
img {
    content-visibility: auto;
    contain-intrinsic-size: 300px 200px;
}

/* Optimize font rendering */
body {
    text-rendering: optimizeSpeed;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* Optimize layout calculations */
.main-content {
    contain: layout;
}

/* Optimize paint operations */
.card-background,
.surface-color {
    contain: paint;
}

/* Critical rendering path optimization */
.above-fold {
    contain: layout style;
}

.below-fold {
    content-visibility: auto;
    contain-intrinsic-size: 0 500px;
}

/* Memory optimization */
.story-viewer {
    contain: strict;
}

/* Optimize transforms for mobile */
@media (max-width: 767.98px) {
    .feed-card:hover {
        transform: translateZ(0) scale(1.02);
    }

    .story-item:hover {
        transform: translateZ(0) scale(1.05);
    }
}

/* Battery optimization for mobile */
@media (max-width: 767.98px) and (prefers-reduced-motion: no-preference) {
    .story-item {
        animation-duration: 4s; /* Slower animations save battery */
    }

    .loading-skeleton {
        animation-duration: 2s;
    }
}

/* Network-aware optimizations */
@media (prefers-reduced-data: reduce) {
    .story-item {
        animation: none;
    }

    .loading-skeleton {
        background: var(--surface-color);
        animation: none;
    }

    .feed-card:hover {
        box-shadow: none;
    }
}

/* ===== NAVIGATION STYLES ===== */

/* Sidebar navigation */
.nav-menu {
    list-style: none;
    padding: 0;
    margin: 0;
    display: grid;
    gap: var(--spacing-sm);
}

.nav-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-md);
    border-radius: var(--border-radius-md);
    transition: all var(--transition-fast);
    cursor: pointer;
    text-decoration: none;
    color: var(--text-secondary);
    min-height: 44px;
    position: relative;
    overflow: hidden;
    font-weight: var(--font-weight-medium);
}

.nav-item:hover {
    background: var(--surface-color);
    color: var(--text-primary);
    transform: translateX(4px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.nav-item.active {
    background: var(--primary-color);
    color: white;
    transform: translateX(8px);
    box-shadow: 0 8px 25px rgba(255, 112, 67, 0.3);
}

.nav-item::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    height: 100%;
    width: 4px;
    background: var(--primary-color);
    transform: scaleY(0);
    transition: transform var(--transition-fast);
    border-radius: 0 var(--border-radius-sm) var(--border-radius-sm) 0;
}

.nav-item.active::before {
    transform: scaleY(1);
}

.nav-item:hover::before {
    transform: scaleY(0.5);
    background: var(--primary-light);
}

.nav-icon {
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: transform var(--transition-fast);
}

.nav-icon svg {
    width: 100%;
    height: 100%;
}

.nav-item:hover .nav-icon {
    transform: scale(1.1);
}

.nav-item.active .nav-icon {
    transform: scale(1.2);
}

/* Navigation badges for notifications */
.nav-item .badge {
    position: absolute;
    top: 8px;
    right: 8px;
    background: var(--accent-color);
    color: white;
    font-size: var(--font-size-xs);
    font-weight: var(--font-weight-bold);
    padding: 2px 6px;
    border-radius: var(--border-radius-full);
    min-width: 18px;
    height: 18px;
    display: flex;
    align-items: center;
    justify-content: center;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}

/* ===== RESPONSIVE UTILITIES ===== */

/* Touch targets for mobile */
@media (max-width: 767.98px) {
    .nav-item,
    .mobile-nav-item,
    .story-item,
    .btn-base {
        min-height: 44px;
        min-width: 44px;
    }

    .content-section {
        border-radius: var(--border-radius-md);
        margin-bottom: var(--gap-mobile);
    }

    .feed-header,
    .feed-content {
        padding: var(--spacing-md);
    }

    /* Mobile-specific improvements */
    .stories-section {
        margin: 0 calc(-1 * var(--gap-mobile));
        padding: var(--spacing-md) var(--gap-mobile);
        border-radius: 0;
        border-left: none;
        border-right: none;
    }

    .feed-card,
    .post-card {
        border-radius: var(--border-radius-md);
        padding: var(--spacing-md);
    }

    /* Optimize for thumb navigation */
    .mobile-nav-item {
        padding: var(--spacing-sm);
        border-radius: var(--border-radius-lg);
    }
}

/* Large mobile and small tablet optimizations */
@media (min-width: 576px) and (max-width: 767.98px) {
    .content-area {
        padding: var(--spacing-lg);
        gap: var(--gap-lg);
    }

    .stories-container {
        gap: var(--spacing-lg);
    }

    .story-item {
        width: 90px;
        height: 90px;
    }
}

/* Prevent horizontal overflow */
.main-content,
.content-area,
.feed,
.stories-container {
    overflow-x: hidden;
    max-width: 100%;
    box-sizing: border-box;
}

/* Smooth scrolling */
html {
    scroll-behavior: smooth;
}

/* Custom scrollbars */
.sidebar::-webkit-scrollbar,
.trending::-webkit-scrollbar {
    width: 6px;
}

.sidebar::-webkit-scrollbar-track,
.trending::-webkit-scrollbar-track {
    background: transparent;
}

.sidebar::-webkit-scrollbar-thumb,
.trending::-webkit-scrollbar-thumb {
    background: var(--border-color);
    border-radius: 3px;
}

.sidebar::-webkit-scrollbar-thumb:hover,
.trending::-webkit-scrollbar-thumb:hover {
    background: var(--text-muted);
}

/* ===== MODERN SOCIAL MEDIA INTERACTIONS ===== */

/* Smooth infinite scroll behavior */
.feed {
    scroll-behavior: smooth;
}

/* Modern card interactions */
.feed-card,
.post-card {
    cursor: pointer;
    will-change: transform;
    transition: all var(--transition-normal);
}

.feed-card:hover,
.post-card:hover {
    transform: translateY(-4px) scale(1.02);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.2);
    border-color: var(--primary-color);
}

.feed-card:active,
.post-card:active {
    transform: translateY(-2px) scale(1.01);
}

/* Story interactions */
.story-item {
    cursor: pointer;
    will-change: transform;
    transition: all var(--transition-fast);
}

.story-item:hover {
    transform: scale(1.1);
    box-shadow: 0 8px 25px rgba(255, 112, 67, 0.3);
}

.story-item:active {
    transform: scale(1.05);
}

/* Navigation interactions */
.nav-item,
.mobile-nav-item {
    position: relative;
    overflow: hidden;
    transition: all var(--transition-fast);
}

.nav-item::after,
.mobile-nav-item::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: all var(--transition-normal);
}

.nav-item:active::after,
.mobile-nav-item:active::after {
    width: 200px;
    height: 200px;
}

/* Button interactions */
.btn-base {
    position: relative;
    overflow: hidden;
    transition: all var(--transition-fast);
}

.btn-base:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(255, 112, 67, 0.3);
}

.btn-base:active {
    transform: translateY(0);
}

/* Loading states */
.loading-skeleton {
    background: linear-gradient(90deg, var(--card-background) 25%, var(--surface-color) 50%, var(--card-background) 75%);
    background-size: 200% 100%;
    animation: shimmer 1.5s infinite;
    border-radius: var(--border-radius-lg);
    height: 200px;
    margin-bottom: var(--gap-card);
}

@keyframes shimmer {
    0% { background-position: -200% 0; }
    100% { background-position: 200% 0; }
}

/* Micro-interactions */
.create-post-container {
    transition: all var(--transition-normal);
}

.create-post-container:hover {
    background: var(--surface-color);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-lg);
    margin: calc(-1 * var(--spacing-lg));
}

/* Pull-to-refresh indicator */
.pull-to-refresh {
    position: absolute;
    top: -60px;
    left: 50%;
    transform: translateX(-50%);
    background: var(--primary-color);
    color: white;
    padding: var(--spacing-sm) var(--spacing-lg);
    border-radius: var(--border-radius-full);
    font-size: var(--font-size-sm);
    opacity: 0;
    transition: all var(--transition-normal);
}

.pull-to-refresh.active {
    opacity: 1;
    top: var(--spacing-lg);
}

/* Floating action button for mobile */
.fab {
    position: fixed;
    bottom: calc(var(--mobile-nav-height) + var(--spacing-lg));
    right: var(--spacing-lg);
    width: 56px;
    height: 56px;
    background: var(--primary-color);
    border: none;
    border-radius: 50%;
    color: white;
    font-size: var(--font-size-xl);
    box-shadow: 0 8px 25px rgba(255, 112, 67, 0.4);
    cursor: pointer;
    transition: all var(--transition-fast);
    z-index: var(--z-fixed);
    display: none;
}

.fab:hover {
    transform: scale(1.1);
    box-shadow: 0 12px 35px rgba(255, 112, 67, 0.5);
}

@media (max-width: 767.98px) {
    .fab {
        display: flex;
        align-items: center;
        justify-content: center;
    }
}

/* ===================================================================== */
/* Integrated Improvements (formerly layout-improvements.css)            */
/* This section centralizes layout spacing, accessibility & scroll logic */
/* ===================================================================== */

/* Container spacing enhancements */
.cards-container,
.posts-container,
#postsContainer { /* unify card grids */
    display: grid;
    gap: var(--gap-card);
    width: 100%;
}

/* Feature cards responsive grid (landing usage) */
.feature-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: var(--gap-section);
    margin-top: var(--gap-section);
}

/* Z-index clarification */
.modal-backdrop { z-index: var(--z-modal-backdrop); }
.modal, .auth-modal { z-index: var(--z-modal); }
.dropdown-menu, .user-menu { z-index: var(--z-dropdown); }

/* Prevent layout shift on hover */
.card, .feed-card, .nav-item { will-change: transform; }

/* Ultra-small devices */
@media (max-width: 575.98px) {
    .cards-container,
    #postsContainer { gap: var(--spacing-md); }
    .feed-content { padding: var(--spacing-md); gap: var(--spacing-md); }
    .feed-action-btn, .nav-item, .mobile-nav-item { min-height: 44px; min-width: 44px; }
}

/* Small devices */
@media (min-width: 576px) and (max-width: 767.98px) {
    .cards-container,
    #postsContainer { gap: var(--gap-card); }
}

/* Medium+ alignment */
@media (min-width: 768px) {
    .main-content { gap: var(--gap-section); }
}

/* Overflow handling */
.feed-card-content,
.post-content { overflow-wrap: break-word; hyphens: auto; }
.feed-card-content p,
.post-content p { max-width: 100%; overflow: hidden; }

/* Focus ring utility */
.focus-ring:focus-visible { outline: 2px solid var(--primary-color); outline-offset: 2px; z-index: var(--z-dropdown); }

/* Logical spacing helpers */
.card-padding { padding-block: var(--spacing-lg); padding-inline: var(--spacing-lg); }
.card-margin { margin-block-end: var(--gap-card); }

/* Main content smooth scroll container */
.main-content { scroll-behavior: smooth; }

/* Load more / infinite scroll */
.load-more-container { display: flex; justify-content: center; padding: var(--gap-card) 0; margin-top: var(--gap-card); }
.load-more-btn { background: var(--card-background); border: 1px solid var(--border-color); border-radius: var(--border-radius-lg); padding: var(--spacing-md) var(--spacing-xl); color: var(--text-primary); font-weight: var(--font-weight-medium); cursor: pointer; transition: all var(--transition-normal); }
.load-more-btn:hover { background: var(--surface-color); border-color: var(--primary-color); transform: translateY(-2px); }

.loading-indicator { display: flex; justify-content: center; align-items: center; padding: var(--gap-card); color: var(--text-muted); }
.loading-spinner { width: 24px; height: 24px; border: 2px solid var(--border-color); border-top: 2px solid var(--primary-color); border-radius: 50%; animation: spin 1s linear infinite; }
@keyframes spin { 0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); } }

/* Section spacing refinement */
.content-section + .content-section { border-top: 8px solid var(--background-color); margin-top: 0; }

/* Sticky headers (optional use) */
.section-header { position: sticky; top: var(--header-height); background: var(--surface-color); border-bottom: 1px solid var(--divider-color); padding: var(--spacing-md) var(--spacing-lg); z-index: var(--z-sticky); backdrop-filter: blur(10px); }

/* Flex fallback */
@supports not (display: grid) {
    .cards-container,
    #postsContainer { display: flex; flex-direction: column; }
    .feed-card, .post { margin-bottom: var(--gap-card); }
    .feed-card:last-child, .post:last-child { margin-bottom: 0; }
}

/* End integrated improvements */
