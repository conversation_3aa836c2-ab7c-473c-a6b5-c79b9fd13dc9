/* Naroop Modern Animations */

/* Fade In Animations */
.fade-in {
    animation: fadeIn 0.6s ease-out;
}

.fade-in-up {
    animation: fadeInUp 0.8s ease-out;
}

.fade-in-down {
    animation: fadeInDown 0.8s ease-out;
}

.fade-in-left {
    animation: fadeInLeft 0.8s ease-out;
}

.fade-in-right {
    animation: fadeInRight 0.8s ease-out;
}

/* Slide Animations */
.slide-in-left {
    animation: slideInLeft 0.6s ease-out;
}

.slide-in-right {
    animation: slideInRight 0.6s ease-out;
}

.slide-in-up {
    animation: slideInUp 0.6s ease-out;
}

.slide-in-down {
    animation: slideInDown 0.6s ease-out;
}

/* Scale Animations */
.scale-in {
    animation: scaleIn 0.4s ease-out;
}

.scale-in-bounce {
    animation: scaleInBounce 0.6s ease-out;
}

/* Bounce Animation */
.bounce {
    animation: bounce 0.6s ease-in-out;
}

/* Pulse Animation */
.pulse {
    animation: pulse 2s infinite;
}

/* Stagger Children Animation */
.stagger-children > * {
    opacity: 0;
    animation: fadeInUp 0.6s ease-out forwards;
}

.stagger-children > *:nth-child(1) { animation-delay: 0.1s; }
.stagger-children > *:nth-child(2) { animation-delay: 0.2s; }
.stagger-children > *:nth-child(3) { animation-delay: 0.3s; }
.stagger-children > *:nth-child(4) { animation-delay: 0.4s; }
.stagger-children > *:nth-child(5) { animation-delay: 0.5s; }
.stagger-children > *:nth-child(6) { animation-delay: 0.6s; }

/* Hover Animations */
.hover-grow {
    transition: transform var(--transition-normal);
}

.hover-grow:hover {
    transform: scale(1.05);
}

.hover-float {
    transition: transform var(--transition-normal);
}

.hover-float:hover {
    transform: translateY(-5px);
}

.hover-glow {
    transition: box-shadow var(--transition-normal);
}

.hover-glow:hover {
    box-shadow: 0 0 20px rgba(255, 107, 53, 0.3);
}

/* Loading Spinner */
.loading-spinner {
    width: 40px;
    height: 40px;
    border: 3px solid var(--border-color);
    border-top: 3px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto;
}

/* Keyframe Definitions */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInDown {
    from {
        opacity: 0;
        transform: translateY(-30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes fadeInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInLeft {
    from {
        transform: translateX(-100%);
    }
    to {
        transform: translateX(0);
    }
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
    }
    to {
        transform: translateX(0);
    }
}

@keyframes slideInUp {
    from {
        transform: translateY(100%);
    }
    to {
        transform: translateY(0);
    }
}

@keyframes slideInDown {
    from {
        transform: translateY(-100%);
    }
    to {
        transform: translateY(0);
    }
}

@keyframes scaleIn {
    from {
        opacity: 0;
        transform: scale(0.8);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes scaleInBounce {
    0% {
        opacity: 0;
        transform: scale(0.3);
    }
    50% {
        transform: scale(1.05);
    }
    70% {
        transform: scale(0.9);
    }
    100% {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes bounce {
    0%, 20%, 53%, 80%, 100% {
        transform: translateY(0);
    }
    40%, 43% {
        transform: translateY(-30px);
    }
    70% {
        transform: translateY(-15px);
    }
    90% {
        transform: translateY(-4px);
    }
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
    
    .loading-spinner {
        animation: none;
        border-top-color: var(--primary-color);
    }
}

/* Interactive Animations */
.click-effect {
    position: relative;
    overflow: hidden;
}

.click-effect::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.3);
    transform: translate(-50%, -50%);
    transition: width 0.6s, height 0.6s;
}

.click-effect:active::after {
    width: 300px;
    height: 300px;
}

/* Page Transition */
.page-transition {
    animation: pageTransition 0.5s ease-in-out;
}

@keyframes pageTransition {
    0% {
        opacity: 0;
        transform: translateY(20px);
    }
    100% {
        opacity: 1;
        transform: translateY(0);
    }
}
