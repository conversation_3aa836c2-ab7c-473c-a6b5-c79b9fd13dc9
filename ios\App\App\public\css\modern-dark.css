/*
Modern Dark Theme for Naroop
Inspired by the provided React component with Tailwind CSS.
*/

:root {
    --dark-bg-primary: #0F172A; /* slate-900 */
    --dark-bg-secondary: #1E293B; /* slate-800 */
    --dark-bg-header: #020617; /* slate-950 */
    --dark-border-color: #334155; /* slate-700 */
    --dark-text-primary: #F8FAFC; /* slate-50 */
    --dark-text-secondary: #94A3B8; /* slate-400 */
    --dark-accent-primary: #818CF8; /* indigo-400 */
    --dark-accent-secondary: #6366F1; /* indigo-500 */
    --dark-accent-tertiary: #4F46E5; /* indigo-600 */
    --font-main: 'Inter', sans-serif;
}

body.modern-dark {
    background-color: var(--dark-bg-primary);
    color: var(--dark-text-primary);
    font-family: var(--font-main);
}

/* Hide original elements that are replaced */
.modern-dark .header-glass,
.modern-dark .sidebar,
.modern-dark .trending,
.modern-dark .mobile-nav,
.modern-dark .fab,
.modern-dark .preferences-panel,
.modern-dark .post-creation-modal,
.modern-dark #action-modal {
    display: none !important;
}


/* Header */
.modern-dark .header-modern {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem 2rem;
    background-color: var(--dark-bg-header);
    border-bottom: 1px solid var(--dark-border-color);
}

.modern-dark .header-modern .logo-container {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.modern-dark .header-modern .logo {
    font-size: 1.5rem;
    font-weight: 700;
    letter-spacing: -0.025em;
    color: var(--dark-accent-primary);
}

.modern-dark .header-modern .search-container {
    position: relative;
    display: none; /* Hidden on mobile by default */
}

@media (min-width: 768px) {
    .modern-dark .header-modern .search-container {
        display: flex;
        align-items: center;
        width: 256px;
    }
}

.modern-dark .header-modern .search-icon {
    position: absolute;
    left: 0.75rem;
    color: var(--dark-text-secondary);
}

.modern-dark .header-modern .search-input {
    width: 100%;
    padding: 0.5rem 1rem 0.5rem 2.5rem;
    border-radius: 9999px;
    background-color: var(--dark-bg-secondary);
    border: 1px solid var(--dark-border-color);
    font-size: 0.875rem;
    color: var(--dark-text-primary);
    transition: box-shadow 0.2s;
}

.modern-dark .header-modern .search-input:focus {
    outline: none;
    box-shadow: 0 0 0 2px var(--dark-accent-secondary);
}

.modern-dark .header-modern .user-actions {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.modern-dark .header-modern .action-btn {
    padding: 0.5rem;
    border-radius: 9999px;
    background: none;
    border: none;
    color: var(--dark-text-secondary);
    cursor: pointer;
    transition: background-color 0.2s;
}

.modern-dark .header-modern .action-btn:hover {
    background-color: var(--dark-bg-secondary);
}

.modern-dark .header-modern .action-btn .mobile-search-icon {
    display: block;
}

@media (min-width: 768px) {
    .modern-dark .header-modern .action-btn .mobile-search-icon {
        display: none;
    }
}

.modern-dark .header-modern .profile-avatar {
    position: relative;
    width: 2rem;
    height: 2rem;
    border-radius: 9999px;
    border: 2px solid var(--dark-accent-secondary);
    overflow: hidden;
}

.modern-dark .header-modern .profile-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

/* Main Content */
.modern-dark .main-content-modern {
    display: flex;
    flex-direction: column;
    padding: 1rem;
    gap: 1.5rem;
}

@media (min-width: 768px) {
    .modern-dark .main-content-modern {
        flex-direction: row;
        padding: 2rem;
    }
}

/* Sidebar Navigation */
.modern-dark .sidebar-modern {
    display: none; /* Hidden on mobile */
}

@media (min-width: 768px) {
    .modern-dark .sidebar-modern {
        display: flex;
        flex-direction: column;
        width: 20%;
        gap: 0.5rem;
    }
}

.modern-dark .sidebar-modern .nav-menu {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    list-style: none;
    padding: 0;
    margin: 0;
}

.modern-dark .sidebar-modern .nav-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem;
    border-radius: 0.75rem;
    color: var(--dark-text-secondary);
    font-weight: 500;
    text-decoration: none;
    transition: background-color 0.2s, color 0.2s;
}

.modern-dark .sidebar-modern .nav-item:hover {
    background-color: var(--dark-bg-secondary);
}

.modern-dark .sidebar-modern .nav-item.active {
    background-color: var(--dark-bg-secondary);
    color: var(--dark-accent-primary);
}

/* Center Feed */
.modern-dark .feed-modern {
    width: 100%;
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

@media (min-width: 768px) {
    .modern-dark .feed-modern {
        width: 60%;
    }
}

.modern-dark .card {
    background-color: var(--dark-bg-secondary);
    border-radius: 0.5rem;
    padding: 1.5rem;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.modern-dark .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.modern-dark .card-title {
    font-size: 1.25rem;
    font-weight: 700;
}

.modern-dark .view-all-btn {
    font-size: 0.875rem;
    padding: 0.25rem 0.75rem;
    background-color: var(--dark-border-color);
    color: white;
    border-radius: 9999px;
    border: none;
    cursor: pointer;
    transition: background-color 0.2s;
}

.modern-dark .view-all-btn:hover {
    background-color: #4B5563; /* A bit lighter */
}

/* Stories */
.modern-dark .stories-container {
    position: relative;
}

.modern-dark .stories-scroll {
    display: flex;
    gap: 1rem;
    overflow-x: scroll;
    scroll-behavior: smooth;
    -ms-overflow-style: none;  /* IE and Edge */
    scrollbar-width: none;  /* Firefox */
}

.modern-dark .stories-scroll::-webkit-scrollbar {
    display: none; /* Chrome, Safari, Opera */
}

.modern-dark .story-item {
    flex-shrink: 0;
    display: flex;
    flex-direction: column;
    align-items: center;
    cursor: pointer;
    text-align: center;
    width: 4rem;
}

.modern-dark .story-avatar-wrapper {
    position: relative;
    width: 4rem;
    height: 4rem;
    border-radius: 9999px;
    transition: all 0.2s ease-in-out;
}

.modern-dark .story-item:hover .story-avatar-wrapper {
    transform: scale(1.05);
}

.modern-dark .story-avatar-wrapper.has-story {
    border: 2px solid var(--dark-accent-secondary);
    padding: 2px;
}

.modern-dark .story-avatar-wrapper.no-story {
    border: 2px solid var(--dark-border-color);
    padding: 2px;
}

.modern-dark .story-avatar-wrapper.add-story {
    background-color: var(--dark-border-color);
    display: flex;
    align-items: center;
    justify-content: center;
}

.modern-dark .story-item:hover .story-avatar-wrapper.add-story {
    border: 2px solid var(--dark-accent-secondary);
}

.modern-dark .story-avatar-wrapper.add-story .add-icon {
    color: var(--dark-accent-primary);
}

.modern-dark .story-avatar {
    width: 100%;
    height: 100%;
    border-radius: 9999px;
    object-fit: cover;
}

.modern-dark .story-name {
    margin-top: 0.5rem;
    font-size: 0.75rem;
    color: var(--dark-text-secondary);
    transition: color 0.2s;
}

.modern-dark .story-item:hover .story-name {
    color: var(--dark-text-primary);
}

/* Stories hover cleanup: ensure no legacy pseudo-elements or tinted backgrounds leak in */
.modern-dark .story-item,
.modern-dark .story-item:hover {
    background: transparent !important;
    box-shadow: none !important;
}

.modern-dark .story-item::before,
.modern-dark .story-item::after {
    content: none !important;
}

/* Ensure wrapper paints cleanly over any underlying effects */
.modern-dark .story-avatar-wrapper {
    background: transparent;
}

/* Share Story Card */
.modern-dark .share-story-card .card-title {
    color: var(--dark-accent-primary);
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.modern-dark .share-story-card p {
    color: var(--dark-text-secondary);
    margin-bottom: 1rem;
}

.modern-dark .btn-primary {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    background-color: var(--dark-accent-tertiary);
    color: white;
    font-weight: 500;
    border-radius: 9999px;
    border: none;
    cursor: pointer;
    transition: background-color 0.2s;
}

.modern-dark .btn-primary:hover {
    background-color: var(--dark-accent-secondary);
}

/* Feed Card */
.modern-dark .feed-card .card-title {
    color: var(--dark-accent-primary);
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.modern-dark .feed-card .refresh-btn {
    font-size: 0.875rem;
    padding: 0.25rem 0.75rem;
    background-color: var(--dark-border-color);
    color: white;
    border-radius: 9999px;
    border: none;
    cursor: pointer;
    transition: background-color 0.2s;
}

.modern-dark .feed-card .refresh-btn:hover {
    background-color: #4B5563;
}

.modern-dark .empty-feed {
    background-color: var(--dark-bg-primary);
    border-radius: 0.5rem;
    padding: 2rem;
    text-align: center;
}

.modern-dark .empty-feed h3 {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--dark-text-primary);
    margin-bottom: 0.5rem;
}

.modern-dark .empty-feed p {
    color: var(--dark-text-secondary);
    margin-bottom: 1rem;
}

.modern-dark .btn-secondary {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    background-color: var(--dark-border-color);
    color: white;
    font-weight: 500;
    border-radius: 9999px;
    border: none;
    cursor: pointer;
    transition: background-color 0.2s;
}

.modern-dark .btn-secondary:hover {
    background-color: #4B5563;
}

/* Trending Sidebar */
.modern-dark .trending-modern {
    display: none; /* Hidden on mobile */
}

@media (min-width: 768px) {
    .modern-dark .trending-modern {
        display: flex;
        flex-direction: column;
        width: 20%;
        gap: 1rem;
    }
}

.modern-dark .trending-modern .trending-title {
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--dark-accent-primary);
}

.modern-dark .trending-list {
    list-style: none;
    padding: 0;
    margin: 0;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.modern-dark .trending-item {
    padding: 0.75rem;
    border-radius: 0.75rem;
    background-color: var(--dark-bg-secondary);
    cursor: pointer;
    transition: background-color 0.2s;
}

.modern-dark .trending-item:hover {
    background-color: var(--dark-border-color);
}

.modern-dark .trending-item .topic {
    font-weight: 500;
    color: var(--dark-text-primary);
    margin: 0 0 0.25rem 0;
}

.modern-dark .trending-item .post-count {
    font-size: 0.75rem;
    color: var(--dark-text-secondary);
}
