/* Naroop Preferences Styles */

.preferences-panel {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(4px);
    z-index: var(--z-modal);
    display: flex;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-lg);
    opacity: 0;
    visibility: hidden;
    transition: all var(--transition-normal);
}

.preferences-panel.active {
    opacity: 1;
    visibility: visible;
}

.preferences-content {
    background: var(--card-background);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-lg);
    max-width: 500px;
    width: 100%;
    max-height: 80vh;
    overflow-y: auto;
    transform: translateY(20px);
    transition: transform var(--transition-normal);
}

.preferences-panel.active .preferences-content {
    transform: translateY(0);
}

.preferences-section {
    margin-bottom: var(--spacing-lg);
}

.preferences-section:last-child {
    margin-bottom: 0;
}

.preferences-section h3 {
    margin-bottom: var(--spacing-md);
    color: var(--text-primary);
    font-size: var(--font-size-lg);
}

.preference-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-md) 0;
    border-bottom: 1px solid var(--divider-color);
}

.preference-item:last-child {
    border-bottom: none;
}

.preference-label {
    flex: 1;
    margin-right: var(--spacing-md);
}

.preference-label h4 {
    margin: 0 0 var(--spacing-xs) 0;
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-medium);
}

.preference-label p {
    margin: 0;
    font-size: var(--font-size-sm);
    color: var(--text-muted);
}

.preference-control {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.theme-selector {
    display: flex;
    gap: var(--spacing-xs);
    background: var(--surface-color);
    border-radius: var(--border-radius-md);
    padding: var(--spacing-xs);
}

.theme-option {
    padding: var(--spacing-xs) var(--spacing-sm);
    border: none;
    background: none;
    border-radius: var(--border-radius-sm);
    cursor: pointer;
    transition: all var(--transition-fast);
    font-size: var(--font-size-sm);
}

.theme-option.active {
    background: var(--primary-color);
    color: white;
}

.theme-option:hover:not(.active) {
    background: var(--card-background);
}

.font-size-controls {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.font-size-btn {
    width: 32px;
    height: 32px;
    border: 1px solid var(--border-color);
    background: var(--card-background);
    border-radius: var(--border-radius-sm);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all var(--transition-fast);
}

.font-size-btn:hover {
    background: var(--surface-color);
    border-color: var(--primary-color);
}

.font-size-display {
    min-width: 60px;
    text-align: center;
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
}

.toggle-switch {
    position: relative;
    width: 48px;
    height: 24px;
    background: var(--border-color);
    border-radius: 12px;
    cursor: pointer;
    transition: background-color var(--transition-fast);
}

.toggle-switch.active {
    background: var(--primary-color);
}

.toggle-switch::after {
    content: '';
    position: absolute;
    top: 2px;
    left: 2px;
    width: 20px;
    height: 20px;
    background: white;
    border-radius: 50%;
    transition: transform var(--transition-fast);
}

.toggle-switch.active::after {
    transform: translateX(24px);
}

.preferences-actions {
    display: flex;
    gap: var(--spacing-md);
    justify-content: flex-end;
    margin-top: var(--spacing-lg);
    padding-top: var(--spacing-lg);
    border-top: 1px solid var(--divider-color);
}

.reset-preferences-btn {
    color: var(--error-color);
    background: none;
    border: 1px solid var(--error-color);
}

.reset-preferences-btn:hover {
    background: var(--error-color);
    color: white;
}