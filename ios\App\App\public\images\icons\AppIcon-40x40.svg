<?xml version="1.0" encoding="UTF-8"?>
<svg width="40" height="40" viewBox="0 0 40 40" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- Main gradient -->
    <linearGradient id="mainGrad40" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#f59e0b;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#f97316;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#d97706;stop-opacity:1" />
    </linearGradient>

    <!-- Highlight gradient -->
    <linearGradient id="highlight40" x1="0%" y1="0%" x2="100%" y2="50%">
      <stop offset="0%" style="stop-color:#ffffff;stop-opacity:0.3" />
      <stop offset="100%" style="stop-color:#ffffff;stop-opacity:0" />
    </linearGradient>

    <!-- Shadow filter -->
    <filter id="shadow40" x="-20%" y="-20%" width="140%" height="140%">
      <feDropShadow dx="0" dy="0.8" stdDeviation="0.4" flood-color="#000000" flood-opacity="0.3"/>
    </filter>
  </defs>

  <!-- Main background -->
  <rect width="40" height="40" rx="8.8" fill="url(#mainGrad40)" filter="url(#shadow40)"/>

  <!-- Highlight overlay -->
  <rect width="40" height="24" rx="8.8" fill="url(#highlight40)"/>

  <!-- Text shadow -->
  <text x="50%" y="50.8%" dominant-baseline="middle" text-anchor="middle"
        font-family="SF Pro Display, -apple-system, Arial, sans-serif" font-weight="700"
        font-size="18" fill="#000000" opacity="0.2">N</text>

  <!-- Main text -->
  <text x="50%" y="50%" dominant-baseline="middle" text-anchor="middle"
        font-family="SF Pro Display, -apple-system, Arial, sans-serif" font-weight="700"
        font-size="18" fill="white">N</text>
</svg>