<?xml version="1.0" encoding="UTF-8"?>
<svg width="128" height="128" viewBox="0 0 128 128" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- Main gradient -->
    <linearGradient id="mainGrad128" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#f59e0b;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#f97316;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#d97706;stop-opacity:1" />
    </linearGradient>

    <!-- Highlight gradient -->
    <linearGradient id="highlight128" x1="0%" y1="0%" x2="100%" y2="50%">
      <stop offset="0%" style="stop-color:#ffffff;stop-opacity:0.3" />
      <stop offset="100%" style="stop-color:#ffffff;stop-opacity:0" />
    </linearGradient>

    <!-- Shadow filter -->
    <filter id="shadow128" x="-20%" y="-20%" width="140%" height="140%">
      <feDropShadow dx="0" dy="2.56" stdDeviation="1.28" flood-color="#000000" flood-opacity="0.3"/>
    </filter>
  </defs>

  <!-- Main background -->
  <rect width="128" height="128" rx="28.16" fill="url(#mainGrad128)" filter="url(#shadow128)"/>

  <!-- Highlight overlay -->
  <rect width="128" height="76.8" rx="28.16" fill="url(#highlight128)"/>

  <!-- Text shadow -->
  <text x="50%" y="52.56%" dominant-baseline="middle" text-anchor="middle"
        font-family="SF Pro Display, -apple-system, Arial, sans-serif" font-weight="700"
        font-size="57.6" fill="#000000" opacity="0.2">N</text>

  <!-- Main text -->
  <text x="50%" y="50%" dominant-baseline="middle" text-anchor="middle"
        font-family="SF Pro Display, -apple-system, Arial, sans-serif" font-weight="700"
        font-size="57.6" fill="white">N</text>
</svg>