<?xml version="1.0" encoding="UTF-8"?>
<svg width="144" height="144" viewBox="0 0 144 144" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- Main gradient -->
    <linearGradient id="mainGrad144" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#f59e0b;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#f97316;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#d97706;stop-opacity:1" />
    </linearGradient>

    <!-- Highlight gradient -->
    <linearGradient id="highlight144" x1="0%" y1="0%" x2="100%" y2="50%">
      <stop offset="0%" style="stop-color:#ffffff;stop-opacity:0.3" />
      <stop offset="100%" style="stop-color:#ffffff;stop-opacity:0" />
    </linearGradient>

    <!-- Shadow filter -->
    <filter id="shadow144" x="-20%" y="-20%" width="140%" height="140%">
      <feDropShadow dx="0" dy="2.88" stdDeviation="1.44" flood-color="#000000" flood-opacity="0.3"/>
    </filter>
  </defs>

  <!-- Main background -->
  <rect width="144" height="144" rx="31.68" fill="url(#mainGrad144)" filter="url(#shadow144)"/>

  <!-- Highlight overlay -->
  <rect width="144" height="86.39999999999999" rx="31.68" fill="url(#highlight144)"/>

  <!-- Text shadow -->
  <text x="50%" y="52.88%" dominant-baseline="middle" text-anchor="middle"
        font-family="SF Pro Display, -apple-system, Arial, sans-serif" font-weight="700"
        font-size="64.8" fill="#000000" opacity="0.2">N</text>

  <!-- Main text -->
  <text x="50%" y="50%" dominant-baseline="middle" text-anchor="middle"
        font-family="SF Pro Display, -apple-system, Arial, sans-serif" font-weight="700"
        font-size="64.8" fill="white">N</text>
</svg>