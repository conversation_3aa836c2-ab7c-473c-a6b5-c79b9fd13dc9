<?xml version="1.0" encoding="UTF-8"?>
<svg width="512" height="512" viewBox="0 0 512 512" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- Main gradient -->
    <linearGradient id="mainGrad512" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#f59e0b;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#f97316;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#d97706;stop-opacity:1" />
    </linearGradient>

    <!-- Highlight gradient -->
    <linearGradient id="highlight512" x1="0%" y1="0%" x2="100%" y2="50%">
      <stop offset="0%" style="stop-color:#ffffff;stop-opacity:0.3" />
      <stop offset="100%" style="stop-color:#ffffff;stop-opacity:0" />
    </linearGradient>

    <!-- Shadow filter -->
    <filter id="shadow512" x="-20%" y="-20%" width="140%" height="140%">
      <feDropShadow dx="0" dy="10.24" stdDeviation="5.12" flood-color="#000000" flood-opacity="0.3"/>
    </filter>
  </defs>

  <!-- Main background -->
  <rect width="512" height="512" rx="112.64" fill="url(#mainGrad512)" filter="url(#shadow512)"/>

  <!-- Highlight overlay -->
  <rect width="512" height="307.2" rx="112.64" fill="url(#highlight512)"/>

  <!-- Text shadow -->
  <text x="50%" y="60.24%" dominant-baseline="middle" text-anchor="middle"
        font-family="SF Pro Display, -apple-system, Arial, sans-serif" font-weight="700"
        font-size="230.4" fill="#000000" opacity="0.2">N</text>

  <!-- Main text -->
  <text x="50%" y="50%" dominant-baseline="middle" text-anchor="middle"
        font-family="SF Pro Display, -apple-system, Arial, sans-serif" font-weight="700"
        font-size="230.4" fill="white">N</text>
</svg>