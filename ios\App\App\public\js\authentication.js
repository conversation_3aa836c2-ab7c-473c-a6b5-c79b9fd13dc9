// Authentication Module for Naroop
// Handles user authentication flows and state management

import { firebaseAuth } from './firebase-config.js';
import { getUserInitials } from './utils.js';

let authManagerInstance = null;

class AuthenticationManager {
    constructor(authProvider) {
        if (authManagerInstance) {
            return authManagerInstance;
        }

        this.authProvider = authProvider;
        this.currentUser = null;
        this.authStateListeners = [];
        this.init();
        
        authManagerInstance = this;
    }

    async init() {
        if (this.authProvider.initialized) {
            this.setupAuthStateListener();
        } else {
            setTimeout(() => this.init(), 100);
        }
    }

    setupAuthStateListener() {
        this.authProvider.auth.onAuthStateChanged((user) => {
            this.currentUser = user;
            this.notifyAuthStateListeners(user);
        });
    }

    onAuthStateChanged(callback) {
        this.authStateListeners.push(callback);
        if (this.currentUser !== null) {
            callback(this.currentUser);
        }
    }

    notifyAuthStateListeners(user) {
        this.authStateListeners.forEach(callback => {
            try {
                callback(user);
            } catch (error) {
                console.error('Error in auth state listener:', error);
            }
        });
    }

    async signIn(email, password) {
        try {
            const userCredential = await this.authProvider.signInWithEmailAndPassword(email, password);
            return { success: true, user: userCredential.user };
        } catch (error) {
            console.error('Sign in error:', error);
            return { success: false, error: error.message };
        }
    }

    async signUp(email, password) {
        try {
            const userCredential = await this.authProvider.createUserWithEmailAndPassword(email, password);
            return { success: true, user: userCredential.user };
        } catch (error) {
            console.error('Sign up error:', error);
            return { success: false, error: error.message };
        }
    }

    async signOut() {
        try {
            await this.authProvider.signOut();
            return { success: true };
        } catch (error) {
            console.error('Sign out error:', error);
            return { success: false, error: error.message };
        }
    }

    getCurrentUser() {
        return this.currentUser;
    }

    isAuthenticated() {
        return this.currentUser !== null;
    }

    getUserDisplayName() {
        if (!this.currentUser) return 'Guest';
        return this.currentUser.displayName || this.currentUser.email || 'User';
    }

    getUserInitials() {
        const displayName = this.getUserDisplayName();
        return getUserInitials(displayName);
    }
}

// Initialize and export the singleton instance
const authManager = new AuthenticationManager(firebaseAuth);

export { AuthenticationManager, authManager };
export default authManager;
