/**
 * iOS-Specific Behaviors Module
 * Implements native iOS touch behaviors, gestures, and interactions
 * for Capacitor WebView to feel like a native iOS app
 */

class iOSBehaviors {
    constructor() {
        this.isIOS = this.detectIOS();
        this.init();
    }

    detectIOS() {
        // Detect if running in iOS (including Capacitor)
        return /iPad|iPhone|iPod/.test(navigator.userAgent) || 
               (navigator.platform === 'MacIntel' && navigator.maxTouchPoints > 1) ||
               window.Capacitor?.getPlatform() === 'ios';
    }

    init() {
        if (!this.isIOS) return;
        
        console.log('🍎 Initializing iOS behaviors...');
        
        this.setupTouchBehaviors();
        this.setupMomentumScrolling();
        this.setupTextSelection();
        this.setupBounceScrolling();
        this.setupTouchCallouts();
        
        console.log('✅ iOS behaviors initialized');
    }

    setupTouchBehaviors() {
        // Prevent default touch behaviors on body to avoid iOS quirks
        document.body.addEventListener('touchstart', (e) => {
            // Allow touch on interactive elements
            if (this.isInteractiveElement(e.target)) {
                return;
            }
        }, { passive: true });

        document.body.addEventListener('touchmove', (e) => {
            // Prevent body scrolling bounce but allow content scrolling
            if (e.target === document.body) {
                e.preventDefault();
            }
        }, { passive: false });

        // Add iOS-style touch feedback
        document.addEventListener('touchstart', (e) => {
            if (this.isInteractiveElement(e.target)) {
                e.target.classList.add('ios-touch-active');
            }
        }, { passive: true });

        document.addEventListener('touchend', (e) => {
            if (this.isInteractiveElement(e.target)) {
                setTimeout(() => {
                    e.target.classList.remove('ios-touch-active');
                }, 150);
            }
        }, { passive: true });

        document.addEventListener('touchcancel', (e) => {
            if (this.isInteractiveElement(e.target)) {
                e.target.classList.remove('ios-touch-active');
            }
        }, { passive: true });
    }

    setupMomentumScrolling() {
        // Enable iOS momentum scrolling for scrollable elements
        const scrollableElements = [
            '.main-content',
            '.feed-container',
            '#postsContainer',
            '.modal-content',
            '.sidebar',
            '.stories-container'
        ];

        scrollableElements.forEach(selector => {
            const elements = document.querySelectorAll(selector);
            elements.forEach(el => {
                if (el) {
                    el.style.webkitOverflowScrolling = 'touch';
                    el.style.overflowScrolling = 'touch';
                }
            });
        });

        // Apply to dynamically created elements
        const observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                mutation.addedNodes.forEach((node) => {
                    if (node.nodeType === 1) { // Element node
                        scrollableElements.forEach(selector => {
                            if (node.matches && node.matches(selector)) {
                                node.style.webkitOverflowScrolling = 'touch';
                                node.style.overflowScrolling = 'touch';
                            }
                        });
                    }
                });
            });
        });

        observer.observe(document.body, { childList: true, subtree: true });
    }

    setupTextSelection() {
        // Disable text selection on UI elements (iOS expectation)
        const uiElements = [
            'button',
            '.btn',
            '.header',
            '.navigation',
            '.sidebar',
            '.user-actions',
            '.logo',
            '.action-btn'
        ];

        uiElements.forEach(selector => {
            const elements = document.querySelectorAll(selector);
            elements.forEach(el => {
                el.style.webkitUserSelect = 'none';
                el.style.userSelect = 'none';
                el.style.webkitTouchCallout = 'none';
            });
        });

        // Allow text selection in content areas
        const contentElements = [
            '.post-content',
            '.story-content',
            'textarea',
            'input[type="text"]',
            'input[type="email"]',
            '.editable'
        ];

        contentElements.forEach(selector => {
            const elements = document.querySelectorAll(selector);
            elements.forEach(el => {
                el.style.webkitUserSelect = 'text';
                el.style.userSelect = 'text';
            });
        });
    }

    setupBounceScrolling() {
        // Prevent rubber band scrolling on the main body
        // but allow it on specific scrollable containers
        document.body.style.overscrollBehavior = 'none';
        
        // Allow bounce on main content areas
        const bounceElements = document.querySelectorAll('.main-content, .feed-container');
        bounceElements.forEach(el => {
            el.style.overscrollBehavior = 'auto';
        });
    }

    setupTouchCallouts() {
        // Disable iOS touch callouts (long press menus) on UI elements
        document.body.style.webkitTouchCallout = 'none';
        
        // Re-enable for content that should have callouts
        const calloutElements = document.querySelectorAll('a, img, .post-content');
        calloutElements.forEach(el => {
            el.style.webkitTouchCallout = 'default';
        });
    }

    isInteractiveElement(element) {
        const interactiveSelectors = [
            'button',
            'a',
            'input',
            'textarea',
            'select',
            '[role="button"]',
            '.btn',
            '.clickable',
            '.action-btn'
        ];

        return interactiveSelectors.some(selector => 
            element.matches && element.matches(selector)
        ) || element.closest('.clickable, .btn, button');
    }

    // Public method to refresh behaviors for dynamically added content
    refresh() {
        this.setupMomentumScrolling();
        this.setupTextSelection();
    }
}

// Auto-initialize when DOM is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        window.iOSBehaviors = new iOSBehaviors();
    });
} else {
    window.iOSBehaviors = new iOSBehaviors();
}

// Export for module use
if (typeof module !== 'undefined' && module.exports) {
    module.exports = iOSBehaviors;
}
