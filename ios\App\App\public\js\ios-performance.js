/**
 * iOS Performance Optimization System
 * Optimizes JavaScript, CSS, and assets for fast loading and smooth iOS performance
 */

class iOSPerformance {
    constructor() {
        this.isIOS = this.detectIOS();
        this.performanceMetrics = {
            loadTime: 0,
            renderTime: 0,
            memoryUsage: 0,
            fps: 60
        };
        this.optimizations = new Set();
        
        this.init();
    }

    detectIOS() {
        return /iPad|iPhone|iPod/.test(navigator.userAgent) || 
               (navigator.platform === 'MacIntel' && navigator.maxTouchPoints > 1) ||
               window.Capacitor?.getPlatform() === 'ios';
    }

    init() {
        console.log('⚡ Initializing iOS performance optimizations...');

        this.measureInitialPerformance();
        this.optimizeImages();
        this.optimizeScrolling();
        this.optimizeMemoryUsage();
        this.setupLazyLoading();
        this.optimizeNetworkRequests();
        this.setupPerformanceMonitoring();
        
        console.log('✅ iOS performance optimizations initialized');
    }

    measureInitialPerformance() {
        // Measure page load performance
        window.addEventListener('load', () => {
            const navigation = performance.getEntriesByType('navigation')[0];
            this.performanceMetrics.loadTime = navigation.loadEventEnd - navigation.fetchStart;
            
            console.log(`📊 Page load time: ${this.performanceMetrics.loadTime}ms`);
            
            // Measure render performance
            this.measureRenderPerformance();
        });
    }

    measureRenderPerformance() {
        const observer = new PerformanceObserver((list) => {
            const entries = list.getEntries();
            entries.forEach(entry => {
                if (entry.entryType === 'paint') {
                    console.log(`🎨 ${entry.name}: ${entry.startTime}ms`);
                }
            });
        });
        
        observer.observe({ entryTypes: ['paint'] });
    }

    optimizeImages() {
        // Lazy load images
        const images = document.querySelectorAll('img[data-src]');
        
        if ('IntersectionObserver' in window) {
            const imageObserver = new IntersectionObserver((entries, observer) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const img = entry.target;
                        this.loadImage(img);
                        observer.unobserve(img);
                    }
                });
            }, {
                rootMargin: '50px 0px',
                threshold: 0.01
            });

            images.forEach(img => imageObserver.observe(img));
        } else {
            // Fallback for older browsers
            images.forEach(img => this.loadImage(img));
        }

        // Optimize existing images
        this.optimizeExistingImages();
    }

    loadImage(img) {
        const src = img.dataset.src;
        if (!src) return;

        // Create a new image to preload
        const newImg = new Image();
        newImg.onload = () => {
            img.src = src;
            img.classList.remove('lazy');
            img.classList.add('loaded');
        };
        newImg.onerror = () => {
            img.classList.add('error');
        };
        newImg.src = src;
    }

    optimizeExistingImages() {
        const images = document.querySelectorAll('img:not([data-src])');
        
        images.forEach(img => {
            // Add loading="lazy" for native lazy loading
            if (!img.hasAttribute('loading')) {
                img.setAttribute('loading', 'lazy');
            }
            
            // Optimize image rendering
            img.style.imageRendering = 'auto';
            img.style.imageRendering = '-webkit-optimize-contrast';
        });
    }

    optimizeScrolling() {
        // Use passive event listeners for better scroll performance
        const scrollElements = document.querySelectorAll('.main-content, .feed-container, .modal-content');
        
        scrollElements.forEach(element => {
            // Enable hardware acceleration
            element.style.transform = 'translateZ(0)';
            element.style.webkitOverflowScrolling = 'touch';
            
            // Optimize scroll events
            let ticking = false;
            element.addEventListener('scroll', () => {
                if (!ticking) {
                    requestAnimationFrame(() => {
                        this.handleScroll(element);
                        ticking = false;
                    });
                    ticking = true;
                }
            }, { passive: true });
        });

        // Optimize window scroll
        this.optimizeWindowScroll();
    }

    optimizeWindowScroll() {
        let ticking = false;
        let lastScrollY = window.scrollY;
        
        window.addEventListener('scroll', () => {
            if (!ticking) {
                requestAnimationFrame(() => {
                    const currentScrollY = window.scrollY;
                    const scrollDirection = currentScrollY > lastScrollY ? 'down' : 'up';
                    
                    // Hide/show elements based on scroll direction
                    this.handleScrollDirection(scrollDirection, currentScrollY);
                    
                    lastScrollY = currentScrollY;
                    ticking = false;
                });
                ticking = true;
            }
        }, { passive: true });
    }

    handleScroll(element) {
        // Implement virtual scrolling for large lists
        if (element.children.length > 100) {
            this.implementVirtualScrolling(element);
        }
    }

    handleScrollDirection(direction, scrollY) {
        // Optimize header visibility
        const header = document.querySelector('.header, .ios-nav-bar');
        if (header) {
            if (direction === 'down' && scrollY > 100) {
                header.style.transform = 'translateY(-100%)';
            } else if (direction === 'up') {
                header.style.transform = 'translateY(0)';
            }
        }
    }

    implementVirtualScrolling(container) {
        // Simple virtual scrolling implementation
        const items = Array.from(container.children);
        const itemHeight = items[0]?.offsetHeight || 100;
        const containerHeight = container.offsetHeight;
        const visibleItems = Math.ceil(containerHeight / itemHeight) + 2;
        
        let startIndex = Math.floor(container.scrollTop / itemHeight);
        let endIndex = Math.min(startIndex + visibleItems, items.length);
        
        // Hide items outside viewport
        items.forEach((item, index) => {
            if (index < startIndex || index > endIndex) {
                item.style.display = 'none';
            } else {
                item.style.display = '';
            }
        });
    }

    optimizeMemoryUsage() {
        // Clean up unused event listeners
        this.cleanupEventListeners();
        
        // Implement object pooling for frequently created objects
        this.setupObjectPooling();
        
        // Monitor memory usage
        this.monitorMemoryUsage();
    }

    cleanupEventListeners() {
        // Remove event listeners from removed elements
        const observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                mutation.removedNodes.forEach((node) => {
                    if (node.nodeType === 1) {
                        // Clone and replace to remove all event listeners
                        if (node.hasAttribute('data-cleanup-listeners')) {
                            const newNode = node.cloneNode(true);
                            node.parentNode?.replaceChild(newNode, node);
                        }
                    }
                });
            });
        });
        
        observer.observe(document.body, { childList: true, subtree: true });
    }

    setupObjectPooling() {
        // Pool for DOM elements that are frequently created/destroyed
        this.elementPool = {
            'post-card': [],
            'story-card': [],
            'modal': []
        };
    }

    getPooledElement(type) {
        if (this.elementPool[type] && this.elementPool[type].length > 0) {
            return this.elementPool[type].pop();
        }
        return null;
    }

    returnToPool(element, type) {
        if (this.elementPool[type]) {
            // Clean element before returning to pool
            element.innerHTML = '';
            element.className = type;
            this.elementPool[type].push(element);
        }
    }

    monitorMemoryUsage() {
        if ('memory' in performance) {
            setInterval(() => {
                const memory = performance.memory;
                this.performanceMetrics.memoryUsage = memory.usedJSHeapSize;
                
                // Trigger garbage collection if memory usage is high
                if (memory.usedJSHeapSize > memory.jsHeapSizeLimit * 0.8) {
                    this.triggerGarbageCollection();
                }
            }, 30000); // Check every 30 seconds
        }
    }

    triggerGarbageCollection() {
        // Force garbage collection by creating and destroying objects
        console.log('🗑️ Triggering garbage collection...');
        
        // Clear caches
        this.clearCaches();
        
        // Clean up pools
        Object.keys(this.elementPool).forEach(key => {
            this.elementPool[key] = this.elementPool[key].slice(0, 5); // Keep only 5 items
        });
    }

    clearCaches() {
        // Clear any internal caches
        if (window.postsCache) {
            window.postsCache.clear();
        }
        if (window.storiesCache) {
            window.storiesCache.clear();
        }
    }

    setupLazyLoading() {
        // Lazy load non-critical JavaScript
        this.lazyLoadModules();
        
        // Lazy load CSS
        this.lazyLoadCSS();
    }

    lazyLoadModules() {
        const lazyModules = [
            { src: '/public/js/analytics.js', condition: () => window.location.search.includes('analytics') },
            { src: '/public/js/admin.js', condition: () => document.body.classList.contains('admin') }
        ];
        
        lazyModules.forEach(module => {
            if (module.condition()) {
                this.loadScript(module.src);
            }
        });
    }

    lazyLoadCSS() {
        const lazyCSS = [
            { href: '/public/css/print.css', media: 'print' },
            { href: '/public/css/admin.css', condition: () => document.body.classList.contains('admin') }
        ];
        
        lazyCSS.forEach(css => {
            if (!css.condition || css.condition()) {
                this.loadCSS(css.href, css.media);
            }
        });
    }

    loadScript(src) {
        return new Promise((resolve, reject) => {
            const script = document.createElement('script');
            script.src = src;
            script.onload = resolve;
            script.onerror = reject;
            document.head.appendChild(script);
        });
    }

    loadCSS(href, media = 'all') {
        const link = document.createElement('link');
        link.rel = 'stylesheet';
        link.href = href;
        link.media = media;
        document.head.appendChild(link);
    }

    optimizeNetworkRequests() {
        // Implement request batching
        this.setupRequestBatching();
        
        // Preload critical resources
        this.preloadCriticalResources();
        
        // Setup service worker for caching
        this.setupServiceWorker();
    }

    setupRequestBatching() {
        this.requestQueue = [];
        this.batchTimeout = null;
        
        // Batch API requests
        this.originalFetch = window.fetch;
        window.fetch = (url, options) => {
            if (this.shouldBatch(url)) {
                return this.batchRequest(url, options);
            }
            return this.originalFetch(url, options);
        };
    }

    shouldBatch(url) {
        return url.includes('/api/') && !url.includes('/auth/');
    }

    batchRequest(url, options) {
        return new Promise((resolve, reject) => {
            this.requestQueue.push({ url, options, resolve, reject });
            
            if (this.batchTimeout) {
                clearTimeout(this.batchTimeout);
            }
            
            this.batchTimeout = setTimeout(() => {
                this.processBatchedRequests();
            }, 50); // Batch requests within 50ms
        });
    }

    processBatchedRequests() {
        const requests = this.requestQueue.splice(0);
        
        // Group similar requests
        const grouped = requests.reduce((acc, req) => {
            const key = req.url.split('?')[0];
            if (!acc[key]) acc[key] = [];
            acc[key].push(req);
            return acc;
        }, {});
        
        // Process each group
        Object.values(grouped).forEach(group => {
            if (group.length === 1) {
                // Single request
                const req = group[0];
                this.originalFetch(req.url, req.options)
                    .then(req.resolve)
                    .catch(req.reject);
            } else {
                // Batch multiple requests
                this.processBatch(group);
            }
        });
    }

    processBatch(requests) {
        // Implement actual batching logic based on your API
        requests.forEach(req => {
            this.originalFetch(req.url, req.options)
                .then(req.resolve)
                .catch(req.reject);
        });
    }

    preloadCriticalResources() {
        const criticalResources = [
            '/public/css/modern-dark.css',
            '/public/js/core.js',
            '/public/images/logo.png'
        ];
        
        criticalResources.forEach(resource => {
            const link = document.createElement('link');
            link.rel = 'preload';
            link.href = resource;
            
            if (resource.endsWith('.css')) {
                link.as = 'style';
            } else if (resource.endsWith('.js')) {
                link.as = 'script';
            } else if (resource.match(/\.(png|jpg|jpeg|webp)$/)) {
                link.as = 'image';
            }
            
            document.head.appendChild(link);
        });
    }

    setupServiceWorker() {
        if ('serviceWorker' in navigator) {
            navigator.serviceWorker.register('/sw.js')
                .then(registration => {
                    console.log('📦 Service Worker registered:', registration);
                })
                .catch(error => {
                    console.log('❌ Service Worker registration failed:', error);
                });
        }
    }

    setupPerformanceMonitoring() {
        // Monitor Core Web Vitals
        this.monitorCoreWebVitals();
        
        // Monitor custom metrics
        this.monitorCustomMetrics();
    }

    monitorCoreWebVitals() {
        // Monitor LCP (Largest Contentful Paint)
        new PerformanceObserver((entryList) => {
            const entries = entryList.getEntries();
            const lastEntry = entries[entries.length - 1];
            console.log('📊 LCP:', lastEntry.startTime);
        }).observe({ entryTypes: ['largest-contentful-paint'] });

        // Monitor FID (First Input Delay)
        new PerformanceObserver((entryList) => {
            const entries = entryList.getEntries();
            entries.forEach(entry => {
                console.log('📊 FID:', entry.processingStart - entry.startTime);
            });
        }).observe({ entryTypes: ['first-input'] });

        // Monitor CLS (Cumulative Layout Shift)
        new PerformanceObserver((entryList) => {
            const entries = entryList.getEntries();
            entries.forEach(entry => {
                if (!entry.hadRecentInput) {
                    console.log('📊 CLS:', entry.value);
                }
            });
        }).observe({ entryTypes: ['layout-shift'] });
    }

    monitorCustomMetrics() {
        // Monitor app-specific performance metrics
        setInterval(() => {
            const metrics = {
                memoryUsage: this.performanceMetrics.memoryUsage,
                activeElements: document.querySelectorAll('*').length,
                eventListeners: this.countEventListeners()
            };
            
            console.log('📊 Custom metrics:', metrics);
        }, 60000); // Every minute
    }

    countEventListeners() {
        // Estimate number of event listeners (simplified)
        return document.querySelectorAll('[onclick], [onload], [onchange]').length;
    }

    // Public API
    getPerformanceMetrics() {
        return { ...this.performanceMetrics };
    }

    optimizeElement(element) {
        element.style.willChange = 'transform';
        element.style.transform = 'translateZ(0)';
        return element;
    }

    preloadImage(src) {
        const img = new Image();
        img.src = src;
        return img;
    }
}

// Auto-initialize when DOM is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        window.iOSPerformance = new iOSPerformance();
    });
} else {
    window.iOSPerformance = new iOSPerformance();
}

// Export for module use
if (typeof module !== 'undefined' && module.exports) {
    module.exports = iOSPerformance;
}
