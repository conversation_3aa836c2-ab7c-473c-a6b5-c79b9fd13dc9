/**
 * iOS Pull-to-Refresh Implementation
 * Provides native iOS-style pull-to-refresh functionality
 */

class iOSPullToRefresh {
    constructor(element, onRefresh, options = {}) {
        this.element = element || document.querySelector('.main-content, .feed-container');
        this.onRefresh = onRefresh || this.defaultRefresh.bind(this);
        
        // Configuration options
        this.options = {
            threshold: 80,           // Distance to trigger refresh
            maxPull: 120,           // Maximum pull distance
            resistance: 3,          // Pull resistance factor
            snapBackDuration: 300,  // Snap back animation duration
            refreshDuration: 2000,  // Minimum refresh duration
            ...options
        };

        this.isIOS = this.detectIOS();
        this.isRefreshing = false;
        this.isPulling = false;
        this.startY = 0;
        this.currentY = 0;
        this.pullDistance = 0;
        this.refreshIndicator = null;

        this.init();
    }

    detectIOS() {
        return /iPad|iPhone|iPod/.test(navigator.userAgent) || 
               (navigator.platform === 'MacIntel' && navigator.maxTouchPoints > 1) ||
               window.Capacitor?.getPlatform() === 'ios';
    }

    init() {
        if (!this.element) {
            console.warn('Pull-to-refresh: No target element found');
            return;
        }

        console.log('🔄 Initializing iOS pull-to-refresh...');

        this.createRefreshIndicator();
        this.setupEventListeners();
        this.setupAutoRefresh();
        
        console.log('✅ iOS pull-to-refresh initialized');
    }

    createRefreshIndicator() {
        this.refreshIndicator = document.createElement('div');
        this.refreshIndicator.className = 'ios-refresh-indicator';
        this.refreshIndicator.innerHTML = `
            <div class="ios-refresh-spinner">
                <svg class="ios-refresh-icon" viewBox="0 0 24 24" width="24" height="24">
                    <path d="M12 2v4m0 12v4M4.93 4.93l2.83 2.83m8.48 8.48l2.83 2.83M2 12h4m12 0h4M4.93 19.07l2.83-2.83m8.48-8.48l2.83-2.83" 
                          stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
                </svg>
                <div class="ios-refresh-text">Pull to refresh</div>
            </div>
        `;

        // Insert at the beginning of the target element
        this.element.insertBefore(this.refreshIndicator, this.element.firstChild);
    }

    setupEventListeners() {
        // Touch events for pull gesture
        this.element.addEventListener('touchstart', this.handleTouchStart.bind(this), { passive: true });
        this.element.addEventListener('touchmove', this.handleTouchMove.bind(this), { passive: false });
        this.element.addEventListener('touchend', this.handleTouchEnd.bind(this), { passive: true });
        this.element.addEventListener('touchcancel', this.handleTouchCancel.bind(this), { passive: true });

        // Mouse events for desktop testing
        if (!this.isIOS) {
            this.element.addEventListener('mousedown', this.handleMouseDown.bind(this));
            this.element.addEventListener('mousemove', this.handleMouseMove.bind(this));
            this.element.addEventListener('mouseup', this.handleMouseUp.bind(this));
            this.element.addEventListener('mouseleave', this.handleMouseUp.bind(this));
        }
    }

    setupAutoRefresh() {
        // Auto-refresh on page visibility change (when user returns to app)
        document.addEventListener('visibilitychange', () => {
            if (!document.hidden && !this.isRefreshing) {
                const lastRefresh = localStorage.getItem('naroop_last_refresh');
                const now = Date.now();
                const fiveMinutes = 5 * 60 * 1000;
                
                if (!lastRefresh || (now - parseInt(lastRefresh)) > fiveMinutes) {
                    setTimeout(() => {
                        this.triggerRefresh();
                    }, 1000);
                }
            }
        });
    }

    handleTouchStart(e) {
        if (this.isRefreshing || this.element.scrollTop > 0) return;
        
        this.startY = e.touches[0].clientY;
        this.isPulling = false;
    }

    handleTouchMove(e) {
        if (this.isRefreshing || this.element.scrollTop > 0) return;

        this.currentY = e.touches[0].clientY;
        const deltaY = this.currentY - this.startY;

        if (deltaY > 0 && this.element.scrollTop === 0) {
            e.preventDefault();
            this.isPulling = true;
            
            // Calculate pull distance with resistance
            this.pullDistance = Math.min(deltaY / this.options.resistance, this.options.maxPull);
            
            this.updateRefreshIndicator();
            this.updateElementTransform();
            
            // Trigger haptic feedback at threshold
            if (this.pullDistance >= this.options.threshold && !this.thresholdReached) {
                this.thresholdReached = true;
                if (window.iOSHaptics) {
                    window.iOSHaptics.light();
                }
                
                // Dispatch custom event
                document.dispatchEvent(new CustomEvent('pullToRefreshTriggered'));
            }
        }
    }

    handleTouchEnd(e) {
        if (!this.isPulling) return;

        this.isPulling = false;
        this.thresholdReached = false;

        if (this.pullDistance >= this.options.threshold && !this.isRefreshing) {
            this.triggerRefresh();
        } else {
            this.snapBack();
        }
    }

    handleTouchCancel(e) {
        this.handleTouchEnd(e);
    }

    // Mouse events for desktop testing
    handleMouseDown(e) {
        if (this.isRefreshing || this.element.scrollTop > 0) return;
        
        this.startY = e.clientY;
        this.isPulling = false;
        this.isMouseDown = true;
    }

    handleMouseMove(e) {
        if (!this.isMouseDown || this.isRefreshing || this.element.scrollTop > 0) return;

        this.currentY = e.clientY;
        const deltaY = this.currentY - this.startY;

        if (deltaY > 0 && this.element.scrollTop === 0) {
            e.preventDefault();
            this.isPulling = true;
            
            this.pullDistance = Math.min(deltaY / this.options.resistance, this.options.maxPull);
            
            this.updateRefreshIndicator();
            this.updateElementTransform();
        }
    }

    handleMouseUp(e) {
        if (!this.isMouseDown) return;
        
        this.isMouseDown = false;
        
        if (!this.isPulling) return;

        this.isPulling = false;

        if (this.pullDistance >= this.options.threshold && !this.isRefreshing) {
            this.triggerRefresh();
        } else {
            this.snapBack();
        }
    }

    updateRefreshIndicator() {
        const progress = Math.min(this.pullDistance / this.options.threshold, 1);
        const rotation = progress * 180;
        
        // Update indicator position and rotation
        this.refreshIndicator.style.transform = `translateY(${this.pullDistance - 60}px)`;
        this.refreshIndicator.style.opacity = Math.min(progress, 1);
        
        const icon = this.refreshIndicator.querySelector('.ios-refresh-icon');
        if (icon) {
            icon.style.transform = `rotate(${rotation}deg)`;
        }
        
        // Update text based on progress
        const textElement = this.refreshIndicator.querySelector('.ios-refresh-text');
        if (textElement) {
            if (progress >= 1) {
                textElement.textContent = 'Release to refresh';
                this.refreshIndicator.classList.add('ready');
            } else {
                textElement.textContent = 'Pull to refresh';
                this.refreshIndicator.classList.remove('ready');
            }
        }
    }

    updateElementTransform() {
        this.element.style.transform = `translateY(${this.pullDistance}px)`;
        this.element.style.transition = 'none';
    }

    async triggerRefresh() {
        if (this.isRefreshing) return;

        this.isRefreshing = true;
        this.pullDistance = this.options.threshold;
        
        // Update UI to refreshing state
        this.refreshIndicator.classList.add('refreshing');
        this.refreshIndicator.querySelector('.ios-refresh-text').textContent = 'Refreshing...';
        
        // Animate to refresh position
        this.element.style.transition = `transform ${this.options.snapBackDuration}ms cubic-bezier(0.25, 0.46, 0.45, 0.94)`;
        this.element.style.transform = `translateY(${this.options.threshold}px)`;
        this.refreshIndicator.style.transform = `translateY(${this.options.threshold - 60}px)`;
        
        // Start spinner animation
        const icon = this.refreshIndicator.querySelector('.ios-refresh-icon');
        if (icon) {
            icon.style.animation = 'iosRefreshSpin 1s linear infinite';
        }

        // Trigger haptic feedback
        if (window.iOSHaptics) {
            window.iOSHaptics.medium();
        }

        // Dispatch refresh start event
        document.dispatchEvent(new CustomEvent('pullToRefreshStart'));

        try {
            // Execute refresh function
            await this.onRefresh();
            
            // Store last refresh time
            localStorage.setItem('naroop_last_refresh', Date.now().toString());
            
        } catch (error) {
            console.error('Refresh failed:', error);
        }

        // Ensure minimum refresh duration for better UX
        setTimeout(() => {
            this.completeRefresh();
        }, this.options.refreshDuration);
    }

    completeRefresh() {
        // Snap back to original position
        this.snapBack();
        
        // Reset state
        setTimeout(() => {
            this.isRefreshing = false;
            this.refreshIndicator.classList.remove('refreshing', 'ready');
            
            const icon = this.refreshIndicator.querySelector('.ios-refresh-icon');
            if (icon) {
                icon.style.animation = '';
                icon.style.transform = '';
            }
            
            // Dispatch refresh complete event
            document.dispatchEvent(new CustomEvent('pullToRefreshComplete'));
            
        }, this.options.snapBackDuration);
    }

    snapBack() {
        this.element.style.transition = `transform ${this.options.snapBackDuration}ms cubic-bezier(0.25, 0.46, 0.45, 0.94)`;
        this.element.style.transform = 'translateY(0)';
        this.refreshIndicator.style.transform = 'translateY(-60px)';
        this.refreshIndicator.style.opacity = '0';
        
        this.pullDistance = 0;
        
        // Clear transition after animation
        setTimeout(() => {
            this.element.style.transition = '';
        }, this.options.snapBackDuration);
    }

    async defaultRefresh() {
        // Default refresh implementation
        console.log('🔄 Refreshing content...');
        
        // Refresh posts if posts module is available
        if (window.postsManager && typeof window.postsManager.refreshPosts === 'function') {
            await window.postsManager.refreshPosts();
        }
        
        // Refresh stories if stories module is available
        if (window.storiesManager && typeof window.storiesManager.refreshStories === 'function') {
            await window.storiesManager.refreshStories();
        }
        
        // Reload page as fallback
        if (!window.postsManager && !window.storiesManager) {
            window.location.reload();
        }
    }

    // Public methods
    destroy() {
        if (this.refreshIndicator && this.refreshIndicator.parentNode) {
            this.refreshIndicator.parentNode.removeChild(this.refreshIndicator);
        }
    }

    setRefreshFunction(fn) {
        this.onRefresh = fn;
    }
}

// Auto-initialize for main content areas
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        window.iOSPullToRefresh = new iOSPullToRefresh();
    });
} else {
    window.iOSPullToRefresh = new iOSPullToRefresh();
}

// Export for module use
if (typeof module !== 'undefined' && module.exports) {
    module.exports = iOSPullToRefresh;
}
