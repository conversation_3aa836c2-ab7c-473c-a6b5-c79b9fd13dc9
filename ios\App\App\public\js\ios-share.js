/**
 * iOS Share Sheet Integration
 * Provides native iOS sharing functionality for posts and stories
 */

class iOSShare {
    constructor() {
        this.isIOS = this.detectIOS();
        this.isCapacitor = window.Capacitor !== undefined;
        this.sharePlugin = null;
        
        this.init();
    }

    detectIOS() {
        return /iPad|iPhone|iPod/.test(navigator.userAgent) || 
               (navigator.platform === 'MacIntel' && navigator.maxTouchPoints > 1) ||
               window.Capacitor?.getPlatform() === 'ios';
    }

    async init() {
        console.log('📤 Initializing iOS share functionality...');

        // Try to use Capacitor Share plugin if available
        if (this.isCapacitor && window.Capacitor.Plugins.Share) {
            this.sharePlugin = window.Capacitor.Plugins.Share;
            console.log('✅ Using Capacitor Share plugin');
        } else {
            console.log('📱 Using Web Share API fallback');
        }

        this.setupShareButtons();
        this.setupShareEvents();
        
        console.log('✅ iOS share functionality initialized');
    }

    setupShareButtons() {
        // Add share buttons to existing posts
        this.addShareButtonsToExistingPosts();
        
        // Handle dynamically added posts
        const observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                mutation.addedNodes.forEach((node) => {
                    if (node.nodeType === 1) {
                        if (node.classList.contains('post-card') || node.classList.contains('story-card')) {
                            this.addShareButtonToPost(node);
                        }
                        // Check child elements
                        const posts = node.querySelectorAll('.post-card, .story-card');
                        posts.forEach(post => this.addShareButtonToPost(post));
                    }
                });
            });
        });

        observer.observe(document.body, { childList: true, subtree: true });
    }

    addShareButtonsToExistingPosts() {
        const posts = document.querySelectorAll('.post-card, .story-card');
        posts.forEach(post => this.addShareButtonToPost(post));
    }

    addShareButtonToPost(postElement) {
        // Check if share button already exists
        if (postElement.querySelector('.ios-share-btn')) return;

        // Find the post actions container
        let actionsContainer = postElement.querySelector('.post-actions, .story-actions');
        
        if (!actionsContainer) {
            // Create actions container if it doesn't exist
            actionsContainer = document.createElement('div');
            actionsContainer.className = 'post-actions';
            postElement.appendChild(actionsContainer);
        }

        // Create share button
        const shareButton = document.createElement('button');
        shareButton.className = 'ios-share-btn action-btn';
        shareButton.innerHTML = `
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M4 12v8a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2v-8"></path>
                <polyline points="16,6 12,2 8,6"></polyline>
                <line x1="12" y1="2" x2="12" y2="15"></line>
            </svg>
            <span>Share</span>
        `;
        shareButton.title = 'Share this post';

        // Add click handler
        shareButton.addEventListener('click', (e) => {
            e.preventDefault();
            e.stopPropagation();
            this.sharePost(postElement);
        });

        // Add to actions container
        actionsContainer.appendChild(shareButton);
    }

    setupShareEvents() {
        // Listen for custom share events
        document.addEventListener('sharePost', (e) => {
            this.sharePost(e.detail.postElement, e.detail.customData);
        });

        document.addEventListener('shareStory', (e) => {
            this.shareStory(e.detail.storyElement, e.detail.customData);
        });

        // Add share functionality to existing share buttons
        document.addEventListener('click', (e) => {
            if (e.target.matches('.share-btn, .share-button') || e.target.closest('.share-btn, .share-button')) {
                e.preventDefault();
                const postElement = e.target.closest('.post-card, .story-card');
                if (postElement) {
                    this.sharePost(postElement);
                }
            }
        });
    }

    async sharePost(postElement, customData = null) {
        try {
            // Extract post data
            const postData = this.extractPostData(postElement);
            
            // Merge with custom data if provided
            const shareData = { ...postData, ...customData };

            // Trigger haptic feedback
            if (window.iOSHaptics) {
                window.iOSHaptics.medium();
            }

            // Attempt native share
            const success = await this.nativeShare(shareData);
            
            if (success) {
                // Track share event
                this.trackShareEvent('post', shareData);
                
                // Show success feedback
                this.showShareSuccess();
            }

        } catch (error) {
            console.error('Share failed:', error);
            this.showShareError();
        }
    }

    async shareStory(storyElement, customData = null) {
        try {
            // Extract story data
            const storyData = this.extractStoryData(storyElement);
            
            // Merge with custom data if provided
            const shareData = { ...storyData, ...customData };

            // Trigger haptic feedback
            if (window.iOSHaptics) {
                window.iOSHaptics.medium();
            }

            // Attempt native share
            const success = await this.nativeShare(shareData);
            
            if (success) {
                // Track share event
                this.trackShareEvent('story', shareData);
                
                // Show success feedback
                this.showShareSuccess();
            }

        } catch (error) {
            console.error('Share failed:', error);
            this.showShareError();
        }
    }

    async nativeShare(shareData) {
        try {
            if (this.sharePlugin) {
                // Use Capacitor Share plugin
                await this.sharePlugin.share({
                    title: shareData.title,
                    text: shareData.text,
                    url: shareData.url,
                    dialogTitle: 'Share this story'
                });
                return true;
            } else if (navigator.share) {
                // Use Web Share API
                await navigator.share({
                    title: shareData.title,
                    text: shareData.text,
                    url: shareData.url
                });
                return true;
            } else {
                // Fallback to clipboard
                return await this.fallbackShare(shareData);
            }
        } catch (error) {
            if (error.name !== 'AbortError') {
                // User didn't cancel, try fallback
                return await this.fallbackShare(shareData);
            }
            return false;
        }
    }

    async fallbackShare(shareData) {
        try {
            // Copy to clipboard
            const shareText = `${shareData.title}\n\n${shareData.text}\n\n${shareData.url}`;
            
            if (navigator.clipboard) {
                await navigator.clipboard.writeText(shareText);
                this.showCopySuccess();
                return true;
            } else {
                // Legacy clipboard method
                const textArea = document.createElement('textarea');
                textArea.value = shareText;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);
                this.showCopySuccess();
                return true;
            }
        } catch (error) {
            console.error('Fallback share failed:', error);
            return false;
        }
    }

    extractPostData(postElement) {
        // Extract post information
        const titleElement = postElement.querySelector('.post-title, h3, h2');
        const contentElement = postElement.querySelector('.post-content, .post-text, p');
        const authorElement = postElement.querySelector('.post-author, .author-name');
        const postId = postElement.dataset.postId || postElement.id;

        const title = titleElement ? titleElement.textContent.trim() : 'Check out this story on Naroop';
        const content = contentElement ? contentElement.textContent.trim() : '';
        const author = authorElement ? authorElement.textContent.trim() : '';
        
        // Truncate content for sharing
        const truncatedContent = content.length > 100 ? content.substring(0, 100) + '...' : content;
        
        const url = postId ? `${window.location.origin}/post/${postId}` : window.location.href;

        return {
            title: title,
            text: author ? `${truncatedContent}\n\n- ${author}` : truncatedContent,
            url: url,
            type: 'post'
        };
    }

    extractStoryData(storyElement) {
        // Extract story information
        const titleElement = storyElement.querySelector('.story-title, h3, h2');
        const contentElement = storyElement.querySelector('.story-content, .story-text, p');
        const authorElement = storyElement.querySelector('.story-author, .author-name');
        const storyId = storyElement.dataset.storyId || storyElement.id;

        const title = titleElement ? titleElement.textContent.trim() : 'Check out this story on Naroop';
        const content = contentElement ? contentElement.textContent.trim() : '';
        const author = authorElement ? authorElement.textContent.trim() : '';
        
        // Truncate content for sharing
        const truncatedContent = content.length > 100 ? content.substring(0, 100) + '...' : content;
        
        const url = storyId ? `${window.location.origin}/story/${storyId}` : window.location.href;

        return {
            title: title,
            text: author ? `${truncatedContent}\n\n- ${author}` : truncatedContent,
            url: url,
            type: 'story'
        };
    }

    showShareSuccess() {
        this.showToast('Shared successfully! 📤', 'success');
    }

    showShareError() {
        this.showToast('Share failed. Please try again.', 'error');
    }

    showCopySuccess() {
        this.showToast('Link copied to clipboard! 📋', 'success');
    }

    showToast(message, type = 'info') {
        // Create toast notification
        const toast = document.createElement('div');
        toast.className = `ios-toast ios-toast-${type}`;
        toast.textContent = message;
        
        document.body.appendChild(toast);
        
        // Animate in
        requestAnimationFrame(() => {
            toast.classList.add('show');
        });
        
        // Remove after delay
        setTimeout(() => {
            toast.classList.remove('show');
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.parentNode.removeChild(toast);
                }
            }, 300);
        }, 3000);
    }

    trackShareEvent(type, data) {
        // Track sharing analytics
        if (window.analytics) {
            window.analytics.track('content_shared', {
                content_type: type,
                content_id: data.url,
                method: this.sharePlugin ? 'native' : 'web'
            });
        }
        
        // Dispatch custom event
        document.dispatchEvent(new CustomEvent('postShared', {
            detail: { type, data }
        }));
    }

    // Public methods
    async shareCurrentPage() {
        const shareData = {
            title: document.title,
            text: 'Check out Naroop - where we share positive stories and celebrate community!',
            url: window.location.href,
            type: 'page'
        };
        
        return await this.nativeShare(shareData);
    }

    async shareCustom(title, text, url) {
        const shareData = { title, text, url, type: 'custom' };
        return await this.nativeShare(shareData);
    }
}

// Auto-initialize when DOM is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        window.iOSShare = new iOSShare();
    });
} else {
    window.iOSShare = new iOSShare();
}

// Export for module use
if (typeof module !== 'undefined' && module.exports) {
    module.exports = iOSShare;
}
