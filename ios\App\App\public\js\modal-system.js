// Modal System for Naroop
// Handles modal creation, display, and interaction

class ModalSystem {
    constructor() {
        this.activeModal = null;
        this.modalStack = [];
        this.init();
    }

    init() {
        // Create modal container if it doesn't exist
        this.ensureModalContainer();
        
        // Set up event listeners
        this.setupEventListeners();
        
        // Handle escape key
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && this.activeModal) {
                this.closeModal();
            }
        });
    }

    ensureModalContainer() {
        let container = document.getElementById('modal-container');
        if (!container) {
            container = document.createElement('div');
            container.id = 'modal-container';
            container.className = 'modal-container';
            document.body.appendChild(container);
        }
        this.container = container;
    }

    setupEventListeners() {
        // Listen for modal trigger buttons
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('opens-modal')) {
                e.preventDefault();
                this.handleModalTrigger(e.target);
            }
            
            if (e.target.classList.contains('modal-close') || 
                e.target.classList.contains('modal-close-button')) {
                e.preventDefault();
                this.closeModal();
            }
            
            // Close modal when clicking backdrop
            if (e.target.classList.contains('modal-overlay')) {
                this.closeModal();
            }
        });
    }

    handleModalTrigger(trigger) {
        const modalType = trigger.dataset.modalType || 'generic';
        const modalTitle = trigger.dataset.modalTitle || 'Modal';
        const modalText = trigger.dataset.modalText || '';
        const modalData = trigger.dataset.modalData ? JSON.parse(trigger.dataset.modalData) : {};

        switch (modalType) {
            case 'auth':
                this.showAuthModal();
                break;
            case 'confirm':
                this.showConfirmModal(modalTitle, modalText, modalData);
                break;
            case 'info':
                this.showInfoModal(modalTitle, modalText);
                break;
            default:
                this.showGenericModal(modalTitle, modalText);
        }
    }

    showGenericModal(title, content) {
        const modalHTML = `
            <div class="modal-overlay" aria-hidden="false">
                <div class="modal-content" role="dialog" aria-modal="true" aria-labelledby="modal-title">
                    <div class="modal-header">
                        <h2 id="modal-title" class="modal-title">${title}</h2>
                        <button class="modal-close-button" aria-label="Close modal">&times;</button>
                    </div>
                    <div class="modal-body">
                        <p>${content}</p>
                    </div>
                    <div class="modal-footer">
                        <button class="btn-base btn-primary modal-close">OK</button>
                    </div>
                </div>
            </div>
        `;
        
        this.displayModal(modalHTML);
    }

    showInfoModal(title, content) {
        const modalHTML = `
            <div class="modal-overlay" aria-hidden="false">
                <div class="modal-content modal-info" role="dialog" aria-modal="true" aria-labelledby="modal-title">
                    <div class="modal-header">
                        <div class="modal-icon">ℹ️</div>
                        <h2 id="modal-title" class="modal-title">${title}</h2>
                        <button class="modal-close-button" aria-label="Close modal">&times;</button>
                    </div>
                    <div class="modal-body">
                        <p>${content}</p>
                    </div>
                    <div class="modal-footer">
                        <button class="btn-base btn-primary modal-close">Got it</button>
                    </div>
                </div>
            </div>
        `;
        
        this.displayModal(modalHTML);
    }

    showConfirmModal(title, content, options = {}) {
        const confirmText = options.confirmText || 'Confirm';
        const cancelText = options.cancelText || 'Cancel';
        const onConfirm = options.onConfirm || (() => {});
        const onCancel = options.onCancel || (() => {});

        const modalHTML = `
            <div class="modal-overlay" aria-hidden="false">
                <div class="modal-content modal-confirm" role="dialog" aria-modal="true" aria-labelledby="modal-title">
                    <div class="modal-header">
                        <div class="modal-icon">⚠️</div>
                        <h2 id="modal-title" class="modal-title">${title}</h2>
                        <button class="modal-close-button" aria-label="Close modal">&times;</button>
                    </div>
                    <div class="modal-body">
                        <p>${content}</p>
                    </div>
                    <div class="modal-footer">
                        <button class="btn-base btn-secondary modal-cancel">${cancelText}</button>
                        <button class="btn-base btn-primary modal-confirm">${confirmText}</button>
                    </div>
                </div>
            </div>
        `;
        
        this.displayModal(modalHTML);
        
        // Add specific event listeners for confirm modal
        const modal = this.activeModal;
        const confirmBtn = modal.querySelector('.modal-confirm');
        const cancelBtn = modal.querySelector('.modal-cancel');
        
        confirmBtn.addEventListener('click', () => {
            onConfirm();
            this.closeModal();
        });
        
        cancelBtn.addEventListener('click', () => {
            onCancel();
            this.closeModal();
        });
    }

    showAuthModal() {
        // This would integrate with the authentication system
        console.log('Auth modal would be shown here');
        // For now, redirect to landing page
        window.location.href = '/';
    }

    displayModal(modalHTML) {
        // Close existing modal if any
        if (this.activeModal) {
            this.closeModal();
        }

        // Create modal element
        const modalElement = document.createElement('div');
        modalElement.innerHTML = modalHTML;
        const modal = modalElement.firstElementChild;
        
        // Add to container
        this.container.appendChild(modal);
        this.activeModal = modal;
        this.modalStack.push(modal);
        
        // Prevent body scroll
        document.body.style.overflow = 'hidden';
        
        // Focus management
        this.trapFocus(modal);
        
        // Animate in
        requestAnimationFrame(() => {
            modal.classList.add('modal-show');
        });
        
        // Announce to screen readers
        this.announceModal(modal);
    }

    closeModal() {
        if (!this.activeModal) return;
        
        const modal = this.activeModal;
        
        // Animate out
        modal.classList.add('modal-hide');
        
        setTimeout(() => {
            // Remove from DOM
            if (modal.parentNode) {
                modal.parentNode.removeChild(modal);
            }
            
            // Update state
            this.modalStack.pop();
            this.activeModal = this.modalStack[this.modalStack.length - 1] || null;
            
            // Restore body scroll if no modals
            if (!this.activeModal) {
                document.body.style.overflow = '';
            }
            
            // Return focus to trigger element
            this.returnFocus();
            
        }, 300); // Match CSS transition duration
    }

    trapFocus(modal) {
        const focusableElements = modal.querySelectorAll(
            'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
        );
        
        if (focusableElements.length === 0) return;
        
        const firstElement = focusableElements[0];
        const lastElement = focusableElements[focusableElements.length - 1];
        
        // Focus first element
        firstElement.focus();
        
        // Trap focus within modal
        modal.addEventListener('keydown', (e) => {
            if (e.key === 'Tab') {
                if (e.shiftKey) {
                    if (document.activeElement === firstElement) {
                        e.preventDefault();
                        lastElement.focus();
                    }
                } else {
                    if (document.activeElement === lastElement) {
                        e.preventDefault();
                        firstElement.focus();
                    }
                }
            }
        });
    }

    announceModal(modal) {
        const title = modal.querySelector('.modal-title');
        if (title) {
            // Create announcement for screen readers
            const announcement = document.createElement('div');
            announcement.setAttribute('aria-live', 'polite');
            announcement.setAttribute('aria-atomic', 'true');
            announcement.className = 'sr-only';
            announcement.textContent = `Modal opened: ${title.textContent}`;
            document.body.appendChild(announcement);
            
            // Remove after announcement
            setTimeout(() => {
                document.body.removeChild(announcement);
            }, 1000);
        }
    }

    returnFocus() {
        // Return focus to the element that opened the modal
        const lastFocused = document.querySelector('[data-modal-trigger]');
        if (lastFocused) {
            lastFocused.focus();
            lastFocused.removeAttribute('data-modal-trigger');
        }
    }
}

// Initialize modal system
const modalSystem = new ModalSystem();

// Export for use in other modules
window.ModalSystem = ModalSystem;
window.modalSystem = modalSystem;
