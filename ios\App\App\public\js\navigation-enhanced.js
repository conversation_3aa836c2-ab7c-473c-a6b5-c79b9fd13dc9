// Enhanced Navigation System for Naroop - 2025 Modern Standards

class NavigationManager {
    constructor() {
        this.currentSection = 'feed';
        this.sections = ['feed', 'explore', 'messages', 'profile', 'communities', 'analytics'];
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.setupSectionSwitching();
        this.setupMobileNavigation();
        this.setupFloatingActionButton();
        this.setupKeyboardNavigation();
    }

    setupEventListeners() {
        // Desktop navigation
        document.querySelectorAll('.nav-item').forEach(item => {
            item.addEventListener('click', (e) => {
                e.preventDefault();
                const section = item.dataset.section;
                this.switchSection(section);
                this.updateActiveNavigation(item);
            });
        });

        // Mobile navigation
        document.querySelectorAll('.mobile-nav-item').forEach(item => {
            item.addEventListener('click', (e) => {
                e.preventDefault();
                const section = item.dataset.section;
                this.switchSection(section);
                this.updateActiveMobileNavigation(item);
            });
        });
    }

    setupSectionSwitching() {
        // Hide all sections except feed initially
        this.sections.forEach(section => {
            const element = document.getElementById(`${section}-section`);
            if (element && section !== 'feed') {
                element.style.display = 'none';
            }
        });
    }

    switchSection(targetSection) {
        if (targetSection === this.currentSection) return;

        // Smooth transition out
        const currentElement = document.getElementById(`${this.currentSection}-section`);
        if (currentElement) {
            currentElement.style.opacity = '0';
            currentElement.style.transform = 'translateY(20px)';
            
            setTimeout(() => {
                currentElement.style.display = 'none';
                this.showSection(targetSection);
            }, 200);
        } else {
            this.showSection(targetSection);
        }

        this.currentSection = targetSection;
        this.updateURL(targetSection);
    }

    showSection(section) {
        const element = document.getElementById(`${section}-section`);
        if (element) {
            element.style.display = 'block';
            element.style.opacity = '0';
            element.style.transform = 'translateY(20px)';
            
            // Force reflow
            element.offsetHeight;
            
            // Smooth transition in
            element.style.transition = 'all 0.3s ease-out';
            element.style.opacity = '1';
            element.style.transform = 'translateY(0)';
        }
    }

    updateActiveNavigation(activeItem) {
        // Remove active class from all desktop nav items
        document.querySelectorAll('.nav-item').forEach(item => {
            item.classList.remove('active');
        });
        
        // Add active class to clicked item
        activeItem.classList.add('active');
    }

    updateActiveMobileNavigation(activeItem) {
        // Remove active class from all mobile nav items
        document.querySelectorAll('.mobile-nav-item').forEach(item => {
            item.classList.remove('active');
        });
        
        // Add active class to clicked item
        activeItem.classList.add('active');
    }

    setupMobileNavigation() {
        // Add haptic feedback for mobile devices
        if ('vibrate' in navigator) {
            document.querySelectorAll('.mobile-nav-item').forEach(item => {
                item.addEventListener('click', () => {
                    navigator.vibrate(50); // Short vibration
                });
            });
        }

        // Handle swipe gestures for section switching
        this.setupSwipeGestures();
    }

    setupSwipeGestures() {
        let startX = 0;
        let startY = 0;
        let endX = 0;
        let endY = 0;

        const contentArea = document.querySelector('.content-area');
        if (!contentArea) return;

        contentArea.addEventListener('touchstart', (e) => {
            startX = e.touches[0].clientX;
            startY = e.touches[0].clientY;
        });

        contentArea.addEventListener('touchend', (e) => {
            endX = e.changedTouches[0].clientX;
            endY = e.changedTouches[0].clientY;
            this.handleSwipe();
        });
    }

    handleSwipe() {
        const deltaX = endX - startX;
        const deltaY = endY - startY;
        const minSwipeDistance = 100;

        // Only handle horizontal swipes
        if (Math.abs(deltaX) > Math.abs(deltaY) && Math.abs(deltaX) > minSwipeDistance) {
            const currentIndex = this.sections.indexOf(this.currentSection);
            
            if (deltaX > 0 && currentIndex > 0) {
                // Swipe right - go to previous section
                this.switchToSectionByIndex(currentIndex - 1);
            } else if (deltaX < 0 && currentIndex < this.sections.length - 1) {
                // Swipe left - go to next section
                this.switchToSectionByIndex(currentIndex + 1);
            }
        }
    }

    switchToSectionByIndex(index) {
        const section = this.sections[index];
        this.switchSection(section);
        
        // Update navigation states
        const navItem = document.querySelector(`[data-section="${section}"]`);
        if (navItem) {
            if (navItem.classList.contains('nav-item')) {
                this.updateActiveNavigation(navItem);
            } else if (navItem.classList.contains('mobile-nav-item')) {
                this.updateActiveMobileNavigation(navItem);
            }
        }
    }

    setupFloatingActionButton() {
        const fab = document.getElementById('mobileFab');
        if (fab) {
            fab.addEventListener('click', () => {
                this.openCreatePostModal();
                
                // Add haptic feedback
                if ('vibrate' in navigator) {
                    navigator.vibrate(100);
                }
            });
        }
    }

    openCreatePostModal() {
        // Trigger create post functionality
        const createPostBtn = document.getElementById('createPostBtn');
        if (createPostBtn) {
            createPostBtn.click();
        }
    }

    setupKeyboardNavigation() {
        document.addEventListener('keydown', (e) => {
            // Handle keyboard shortcuts
            if (e.ctrlKey || e.metaKey) {
                switch(e.key) {
                    case '1':
                        e.preventDefault();
                        this.switchSection('feed');
                        break;
                    case '2':
                        e.preventDefault();
                        this.switchSection('explore');
                        break;
                    case '3':
                        e.preventDefault();
                        this.switchSection('messages');
                        break;
                    case '4':
                        e.preventDefault();
                        this.switchSection('profile');
                        break;
                }
            }
        });
    }

    updateURL(section) {
        // Update URL without page reload
        const url = new URL(window.location);
        url.searchParams.set('section', section);
        window.history.pushState({ section }, '', url);
    }

    // Public method to switch sections programmatically
    navigateTo(section) {
        if (this.sections.includes(section)) {
            this.switchSection(section);
            
            // Update navigation states
            const navItem = document.querySelector(`[data-section="${section}"]`);
            if (navItem) {
                if (navItem.classList.contains('nav-item')) {
                    this.updateActiveNavigation(navItem);
                } else if (navItem.classList.contains('mobile-nav-item')) {
                    this.updateActiveMobileNavigation(navItem);
                }
            }
        }
    }
}

// Initialize navigation when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.navigationManager = new NavigationManager();
    
    // Handle browser back/forward buttons
    window.addEventListener('popstate', (e) => {
        if (e.state && e.state.section) {
            window.navigationManager.navigateTo(e.state.section);
        }
    });
    
    // Check URL for initial section
    const urlParams = new URLSearchParams(window.location.search);
    const initialSection = urlParams.get('section');
    if (initialSection && window.navigationManager.sections.includes(initialSection)) {
        window.navigationManager.navigateTo(initialSection);
    }
});

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = NavigationManager;
}
