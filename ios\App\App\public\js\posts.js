// Posts Module for Naroop
// Handles post creation, display, and interaction

import { naroopCore } from './core.js';
import { authManager } from './authentication.js';
import { getTimeAgo, getUserInitials } from './utils.js';
import notificationSystem from './notification-system.js';
import analytics from './analytics.js';

class PostsManager {
    constructor() {
        this.posts = [];
        this.filtered = [];
        this.isLoading = false;
        this.filters = {
            q: '',
            tag: 'all',
            sort: 'latest',
            range: 'all'
        };
    // Infinite scroll state
    this.page = 1;
    this.pageSize = 10;
    this.observer = null;
    this.isLoadingMore = false;
    this.pendingOpenId = null;
        this.init();
    }

    init() {
        this.setupEventHandlers();
        this.loadPosts();
        
        // Register with core
        naroopCore.registerModule('posts', this);
    }

    setupEventHandlers() {
        // Handle post creation
        document.addEventListener('click', (e) => {
            if (e.target.matches('.create-post-btn, .create-post-btn *')) {
                e.preventDefault();
                this.showCreatePostModal();
            }
            
            if (e.target.matches('.post-action, .post-action *')) {
                e.preventDefault();
                this.handlePostAction(e);
            }

            // Quick view: open when clicking a post (but not when clicking action buttons/links)
            const postEl = e.target.closest('article.post');
            const clickedAction = e.target.closest('.post-action');
            if (postEl && !clickedAction) {
                const postId = postEl.dataset.postId;
                if (postId) {
                    e.preventDefault();
                    this.openQuickView(postId);
                }
            }
        });

        // Handle post form submission
        document.addEventListener('submit', (e) => {
            if (e.target.matches('.create-post-form')) {
                e.preventDefault();
                this.handleCreatePost(e);
            }
        });

        // Explore filter UI handlers
        const searchInput = document.querySelector('#explore-search');
        const sortSelect = document.querySelector('.filter-sort-select');
        const rangeSelect = document.querySelector('.filter-range-select');
        const chipBar = document.querySelector('.filter-chips');
        const refreshBtn = document.querySelector('[data-explore-refresh]');

        if (searchInput) {
            searchInput.addEventListener('input', this.handleFilterChange.bind(this));
        }
        if (sortSelect) {
            sortSelect.addEventListener('change', this.handleFilterChange.bind(this));
        }
        if (rangeSelect) {
            rangeSelect.addEventListener('change', this.handleFilterChange.bind(this));
        }
        if (chipBar) {
            chipBar.addEventListener('click', (e) => {
                const chip = e.target.closest('.chip');
                if (!chip) return;
                chipBar.querySelectorAll('.chip').forEach(c => c.setAttribute('aria-pressed', 'false'));
                chip.setAttribute('aria-pressed', 'true');
                this.filters.tag = chip.dataset.tag || 'all';
                this.applyFiltersAndRender();
            });
        }
        if (refreshBtn) {
            refreshBtn.addEventListener('click', () => this.loadPosts());
        }

    // Deep-link handling for quick view (#post-<id>)
    window.addEventListener('hashchange', () => this.handleHashChange());
    }

    async loadPosts() {
        if (this.isLoading) return;
        
        this.isLoading = true;
        this.showLoadingState();

        try {
            // In a real app, this would fetch from the server
            // For now, we'll use mock data or empty state
            // Try API first; if not available (static server), fall back to local JSON
            let response = await fetch(`/api/posts`).catch(() => null);
            if (!response || !response.ok) {
                response = await fetch('/posts.json').catch(() => null);
            }
            
            if (response && response.ok) {
                const data = await response.json();
                // Normalize shape from server to client expectations
                this.posts = data.map(p => ({
                    id: p.id,
                    content: p.content || p.title || '',
                    image: p.image || p.mediaUrl || '',
                    createdAt: p.createdAt,
                    likes: p.likes || 0,
                    comments: p.comments || 0,
                    author: {
                        name: p.username || (p.author && p.author.name) || 'User',
                        id: p.userId || (p.author && p.author.id) || 'unknown'
                    },
                    category: p.category || 'Personal Experience'
                }));
            } else {
                // Show empty state for new users
                this.posts = [];
                console.warn('No posts API or local posts.json available.');
            }
            // Initialize filters from URL and render
            this.initFiltersFromURL();
            this.applyFiltersAndRender();
            this.handleInitialHash();
            
        } catch (error) {
            console.error('Error loading posts:', error);
            this.showErrorState();
        } finally {
            this.isLoading = false;
            this.hideLoadingState();
        }
    }

    initFiltersFromURL() {
        const params = new URLSearchParams(window.location.search);
        this.filters.q = params.get('q') || '';
        this.filters.tag = params.get('tag') || 'all';
        this.filters.sort = params.get('sort') || 'latest';
        this.filters.range = params.get('range') || 'all';

        // Hydrate UI
        const searchInput = document.querySelector('#explore-search');
        const sortSelect = document.querySelector('.filter-sort-select');
        const rangeSelect = document.querySelector('.filter-range-select');
        const chipBar = document.querySelector('.filter-chips');
        if (searchInput) searchInput.value = this.filters.q;
        if (sortSelect) sortSelect.value = this.filters.sort;
        if (rangeSelect) rangeSelect.value = this.filters.range;
        if (chipBar) {
            const target = chipBar.querySelector(`[data-tag="${CSS.escape(this.filters.tag)}"]`) || chipBar.querySelector('[data-tag="all"]');
            if (target) {
                chipBar.querySelectorAll('.chip').forEach(c => c.setAttribute('aria-pressed', 'false'));
                target.setAttribute('aria-pressed', 'true');
            }
        }
    }

    handleInitialHash() {
        const hash = window.location.hash || '';
        const match = hash.match(/^#post-(.+)$/);
        if (match) {
            const id = match[1];
            const list = this.getVisibleList();
            if (list.find(p => p.id === id)) {
                this.openQuickView(id);
            } else {
                // If not yet visible due to pagination, increase pages until found
                this.pendingOpenId = id;
                this.expandUntilVisible(id);
            }
        }
    }

    handleHashChange() {
        const hash = window.location.hash || '';
        const match = hash.match(/^#post-(.+)$/);
        if (match) {
            const id = match[1];
            this.openQuickView(id);
        } else {
            // No hash -> close quick view if open
            if (window.modalSystem?.activeModal?.querySelector('.quick-view-modal')) {
                window.modalSystem.closeModal();
            }
        }
    }

    getVisibleList() {
        return this.filtered && this.filtered.length ? this.filtered : this.posts;
    }

    expandUntilVisible(postId) {
        const list = this.getVisibleList();
        let idx = list.findIndex(p => p.id === postId);
        const needsMore = () => idx === -1 && (this.page * this.pageSize) < list.length;
        while (needsMore()) {
            this.page += 1;
            idx = list.findIndex(p => p.id === postId);
        }
        if (idx !== -1) {
            this.renderPosts();
            this.openQuickView(postId);
            this.pendingOpenId = null;
        }
    }

    openQuickView(postId) {
        const list = this.getVisibleList();
        const index = list.findIndex(p => p.id === postId);
        if (index === -1) return;
        const post = list[index];
        try { analytics.track('qv_open', { postId, index, total: list.length }); } catch {}

        // Build modal content (reuse button classes for actions)
        const modalHTML = `
            <div class="modal-overlay" aria-hidden="false">
                <div class="modal-content quick-view-modal" role="dialog" aria-modal="true" aria-labelledby="qv-title">
                    <div class="modal-header">
                        <h2 id="qv-title" class="modal-title">${post.author?.name || 'User'}</h2>
                        <button class="modal-close-button" aria-label="Close modal">&times;</button>
                    </div>
                    <div class="modal-body">
                        <article class="post" data-post-id="${post.id}">
                            <div class="post-header">
                                <div class="post-avatar">${getUserInitials(post.author?.name || 'U')}</div>
                                <div class="post-meta">
                                    <div class="post-author">${post.author?.name || 'User'}</div>
                                    <div class="post-time">${getTimeAgo(post.createdAt)}</div>
                                </div>
                            </div>
                            <div class="post-content">
                                <p>${post.content || ''}</p>
                                ${post.image ? `<img src="${post.image}" alt="Post image" class="post-image" loading="lazy">` : ''}
                            </div>
                            <div class="post-actions">
                                <button class="post-action" data-action="like" data-post-id="${post.id}">
                                    <span class="icon">❤️</span>
                                    <span class="count">${post.likes || 0}</span>
                                </button>
                                <button class="post-action" data-action="comment" data-post-id="${post.id}">
                                    <span class="icon">💬</span>
                                    <span class="count">${post.comments || 0}</span>
                                </button>
                                <button class="post-action" data-action="share" data-post-id="${post.id}">
                                    <span class="icon">🔗</span>
                                    <span>Share</span>
                                </button>
                            </div>
                        </article>
                    </div>
                    <div class="modal-footer" style="display:flex;justify-content:space-between;gap:12px;">
                        <button class="btn-base btn-secondary" data-qv-prev>&larr; Previous</button>
                        <button class="btn-base btn-secondary" data-qv-next>Next &rarr;</button>
                    </div>
                </div>
            </div>
        `;

        window.modalSystem?.displayModal(modalHTML);
        // Update hash for deep link
        this.updatePostHash(postId);

        // Wire prev/next buttons
        const modal = window.modalSystem?.activeModal;
        if (modal) {
            const prevBtn = modal.querySelector('[data-qv-prev]');
            const nextBtn = modal.querySelector('[data-qv-next]');
            prevBtn?.addEventListener('click', () => this.navigateQuickView(-1, postId));
            nextBtn?.addEventListener('click', () => this.navigateQuickView(1, postId));

            // Update disabled state
            this.updateNavButtonsState(prevBtn, nextBtn, index, list.length);

            // Close handlers to clear hash
            const closeEls = modal.querySelectorAll('.modal-close, .modal-close-button');
            closeEls.forEach(el => el.addEventListener('click', () => this.clearPostHash(), { capture: true }));

            // Keyboard navigation
            const onKey = (e) => {
                if (!window.modalSystem?.activeModal?.querySelector('.quick-view-modal')) return;
                if (e.key === 'ArrowLeft') this.navigateQuickView(-1, postId);
                if (e.key === 'ArrowRight') this.navigateQuickView(1, postId);
            };
            document.addEventListener('keydown', onKey);
            // Clean up listener after modal closes
            const observer = new MutationObserver(() => {
                if (!document.body.contains(modal)) {
                    document.removeEventListener('keydown', onKey);
                    observer.disconnect();
                    this.clearPostHash();
                }
            });
            observer.observe(document.body, { childList: true, subtree: true });
        }
    }

    updatePostHash(postId) {
        const url = new URL(window.location.href);
        url.hash = `post-${postId}`;
        history.replaceState({}, '', url);
    }

    clearPostHash() {
        if (window.location.hash.startsWith('#post-')) {
            const url = new URL(window.location.href);
            url.hash = '';
            history.replaceState({}, '', url);
        }
    }

    navigateQuickView(direction, currentId) {
        const list = this.getVisibleList();
        const idx = list.findIndex(p => p.id === currentId);
        if (idx === -1) return;
        const nextIdx = idx + direction;
        if (nextIdx < 0 || nextIdx >= list.length) return;
        try { analytics.track('qv_nav', { direction: direction < 0 ? 'prev' : 'next', fromIndex: idx, toIndex: nextIdx, total: list.length }); } catch {}
        this.openQuickView(list[nextIdx].id);
    }

    updateNavButtonsState(prevBtn, nextBtn, index, length) {
        if (prevBtn) prevBtn.disabled = index <= 0;
        if (nextBtn) nextBtn.disabled = index >= length - 1;
    }

    handleFilterChange() {
        const searchInput = document.querySelector('#explore-search');
        const sortSelect = document.querySelector('.filter-sort-select');
        const rangeSelect = document.querySelector('.filter-range-select');
        this.filters.q = searchInput ? searchInput.value.trim() : '';
        this.filters.sort = sortSelect ? sortSelect.value : 'latest';
        this.filters.range = rangeSelect ? rangeSelect.value : 'all';
        this.applyFiltersAndRender();
    }

    applyFiltersAndRender() {
        // Update URL
        const params = new URLSearchParams();
        if (this.filters.q) params.set('q', this.filters.q);
        if (this.filters.tag && this.filters.tag !== 'all') params.set('tag', this.filters.tag);
        if (this.filters.sort && this.filters.sort !== 'latest') params.set('sort', this.filters.sort);
        if (this.filters.range && this.filters.range !== 'all') params.set('range', this.filters.range);
        const newUrl = `${window.location.pathname}?${params.toString()}`;
        window.history.replaceState({}, '', newUrl);

    // Filter
        const q = this.filters.q.toLowerCase();
        const now = Date.now();
        const inRange = (createdAt) => {
            if (this.filters.range === 'all') return true;
            const age = now - new Date(createdAt).getTime();
            const day = 24 * 60 * 60 * 1000;
            switch (this.filters.range) {
                case 'day': return age <= day;
                case 'week': return age <= 7 * day;
                case 'month': return age <= 30 * day;
                default: return true;
            }
        };

        this.filtered = this.posts.filter(p => {
            const matchesQ = !q || (p.content && p.content.toLowerCase().includes(q)) || (p.author?.name?.toLowerCase().includes(q)) || (p.category?.toLowerCase().includes(q));
            const matchesTag = this.filters.tag === 'all' || p.category === this.filters.tag;
            return matchesQ && matchesTag && inRange(p.createdAt);
        });

        // Sort
        if (this.filters.sort === 'top') {
            this.filtered.sort((a, b) => (b.likes || 0) + (b.comments || 0) - ((a.likes || 0) + (a.comments || 0)));
        } else {
            this.filtered.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));
        }

        // Reset pagination and render
        this.page = 1;
        this.isLoadingMore = false;
        this.teardownInfiniteScroll();
        try { analytics.track('explore_filter_change', { q: this.filters.q ? '[set]' : '', tag: this.filters.tag, sort: this.filters.sort, range: this.filters.range }); } catch {}
        this.renderPosts();
    }

    renderPosts() {
        const postsContainer = document.querySelector('.posts-container');
        if (!postsContainer) return;

        const list = this.filtered && this.filtered.length ? this.filtered : this.posts;

        if (list.length === 0) {
            this.showEmptyState(postsContainer);
            return;
        }
        // Paginate
        const visible = list.slice(0, this.page * this.pageSize);
        const postsHTML = visible.map(post => this.renderPost(post)).join('');
        postsContainer.innerHTML = postsHTML;
        try { analytics.track('explore_render', { count: visible.length, total: list.length, page: this.page, pageSize: this.pageSize }); } catch {}
        
        // Attach sentinel for infinite scroll if more items remain
        const hasMore = visible.length < list.length;
        if (hasMore) {
            const sentinel = document.createElement('div');
            sentinel.className = 'infinite-sentinel';
            sentinel.setAttribute('aria-hidden', 'true');
            sentinel.style.height = '1px';
            sentinel.style.marginTop = '1px';
            postsContainer.appendChild(sentinel);
            this.setupInfiniteScroll(sentinel, list.length);
        } else {
            this.teardownInfiniteScroll();
        }
    }

    renderPost(post) {
        const timeAgo = getTimeAgo(post.createdAt);
        const userInitials = getUserInitials(post.author.name);
        
        return `
            <article class="post" data-post-id="${post.id}">
                <div class="post-header">
                    <div class="post-avatar">${userInitials}</div>
                    <div class="post-meta">
                        <div class="post-author">${post.author.name}</div>
                        <div class="post-time">${timeAgo}</div>
                    </div>
                </div>
                <div class="post-content">
                    <p>${post.content}</p>
                    ${post.image ? `<img src="${post.image}" alt="Post image" class="post-image" loading="lazy">` : ''}
                </div>
                <div class="post-actions">
                    <button class="post-action" data-action="like" data-post-id="${post.id}">
                        <span class="icon">❤️</span>
                        <span class="count">${post.likes || 0}</span>
                    </button>
                    <button class="post-action" data-action="comment" data-post-id="${post.id}">
                        <span class="icon">💬</span>
                        <span class="count">${post.comments || 0}</span>
                    </button>
                    <button class="post-action" data-action="share" data-post-id="${post.id}">
                        <span class="icon">🔗</span>
                        <span>Share</span>
                    </button>
                </div>
            </article>
        `;
    }

    showEmptyState(container) {
        container.innerHTML = `
            <div class="empty-state">
                <div class="empty-state-icon">📝</div>
                <h3>No posts yet</h3>
                <p>Be the first to share a positive story with the community!</p>
                <button class="btn-base btn-primary create-post-btn">
                    Create Your First Post
                </button>
            </div>
        `;
    }

    showLoadingState() {
        const postsContainer = document.querySelector('.posts-container');
        if (postsContainer) {
            postsContainer.innerHTML = `
                <div class="loading-skeleton"></div>
                <div class="loading-skeleton"></div>
                <div class="loading-skeleton"></div>
            `;
        }
    }

    hideLoadingState() {
        const loadingElements = document.querySelectorAll('.loading-skeleton');
        loadingElements.forEach(el => el.remove());
    }

    setupInfiniteScroll(sentinelEl, totalLength) {
        this.teardownInfiniteScroll();
        this.observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    this.loadMore(totalLength);
                }
            });
        }, { rootMargin: '200px 0px 200px 0px', threshold: 0 });
        this.observer.observe(sentinelEl);
    }

    teardownInfiniteScroll() {
        if (this.observer) {
            this.observer.disconnect();
            this.observer = null;
        }
    }

    loadMore(totalLength) {
        if (this.isLoadingMore) return;
        const list = this.filtered && this.filtered.length ? this.filtered : this.posts;
        const visibleCount = this.page * this.pageSize;
        if (visibleCount >= list.length) {
            this.teardownInfiniteScroll();
            return;
        }

        // Show lightweight skeletons at bottom
        const container = document.querySelector('.posts-container');
        if (!container) return;
        this.isLoadingMore = true;
        const skeletonWrapper = document.createElement('div');
        skeletonWrapper.innerHTML = `
            <div class="loading-skeleton"></div>
            <div class="loading-skeleton"></div>
        `;
        container.appendChild(skeletonWrapper);

        // Simulate async delay to avoid jank and show skeleton
        setTimeout(() => {
            skeletonWrapper.remove();
            this.page += 1;
            // Re-render current filtered set with increased page
            this.renderPosts();
            this.isLoadingMore = false;
        }, 250);
    }

    showErrorState() {
        const postsContainer = document.querySelector('.posts-container');
        if (postsContainer) {
            postsContainer.innerHTML = `
                <div class="error-state">
                    <div class="error-icon">⚠️</div>
                    <h3>Unable to load posts</h3>
                    <p>Please check your connection and try again.</p>
                    <button class="btn-base btn-secondary" onclick="location.reload()">
                        Retry
                    </button>
                </div>
            `;
        }
    }

    showCreatePostModal() {
        if (!authManager.isAuthenticated()) {
            // Show sign in modal instead
            window.modalSystem?.showAuthModal();
            return;
        }

        const modalHTML = `
            <div class="modal-overlay">
                <div class="modal-content create-post-modal">
                    <div class="modal-header">
                        <h2>Share Your Story</h2>
                        <button class="modal-close-button">&times;</button>
                    </div>
                    <div class="modal-body">
                        <form class="create-post-form">
                            <div class="form-group">
                                <label for="post-title" class="form-label">Title</label>
                                <input 
                                    type="text" 
                                    id="post-title" 
                                    name="title"
                                    class="form-input"
                                    placeholder="Give your story a short, uplifting title"
                                    maxlength="80"
                                    required
                                />
                            </div>
                            <div class="form-group">
                                <textarea 
                                    class="form-input post-content-input" 
                                    id="post-content"
                                    name="content"
                                    placeholder="Share something positive with the community..."
                                    rows="4"
                                    maxlength="500"
                                    required
                                ></textarea>
                                <div class="character-count">0/500</div>
                            </div>
                            <div class="form-group">
                                <label for="post-image">Add an image (optional)</label>
                                <input type="file" id="post-image" accept="image/*" class="form-input">
                            </div>
                            <div class="form-buttons">
                                <button type="button" class="btn-base btn-secondary modal-close">Cancel</button>
                                <button type="submit" class="btn-base btn-primary">Share Story</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        `;

        // Use modal system if available
        if (window.modalSystem) {
            window.modalSystem.displayModal(modalHTML);
        } else {
            // Fallback: add to body directly
            const modalElement = document.createElement('div');
            modalElement.innerHTML = modalHTML;
            document.body.appendChild(modalElement.firstElementChild);
        }

        // Set up character counter
        const textarea = document.querySelector('.post-content-input');
        const counter = document.querySelector('.character-count');
        
        if (textarea && counter) {
            textarea.addEventListener('input', () => {
                const length = textarea.value.length;
                counter.textContent = `${length}/500`;
                
                if (length > 450) {
                    counter.style.color = 'var(--warning-color)';
                } else {
                    counter.style.color = 'var(--text-muted)';
                }
            });
            // Initialize count immediately
            counter.textContent = `${textarea.value.length}/500`;
        }
    }

    async handleCreatePost(event) {
        const form = event.target;
    const formData = new FormData(form);
        const title = (formData.get('title') || '').toString();
        const content = (formData.get('content') || form.querySelector('.post-content-input')?.value || '').toString();
    const fileInput = form.querySelector('#post-image');
        
        if (!title.trim()) {
            alert('Please enter a title for your story.');
            return;
        }

        if (!content.trim()) {
            alert('Please enter some content for your story.');
            return;
        }

        try {
            const user = authManager.getCurrentUser();
            const uid = user?.uid;
            const username = user?.displayName || (user?.email ? user.email.split('@')[0] : authManager.getUserDisplayName());

            // Optional: upload image first (if any)
            let imageUrl = '';
            const file = fileInput?.files?.[0];
            if (file) {
                try {
                    const upFd = new FormData();
                    upFd.append('image', file);
            const upRes = await fetch(`/api/uploads`, {
                        method: 'POST',
                        body: upFd
                    });
                    if (upRes.ok) {
                        const upData = await upRes.json();
                        imageUrl = upData?.url || '';
                    } else {
                        console.warn('Image upload failed, continuing without image');
                    }
                } catch (e) {
                    console.warn('Image upload error, continuing without image', e);
                }
            }

            const postData = {
                title: title.trim(),
                content: content.trim(),
                userId: uid,
                username,
                ...(imageUrl ? { image: imageUrl } : {})
            };

            // In a real app, this would send to the server
        const response = await fetch(`/api/posts`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(postData)
            });

            if (response.ok) {
                const result = await response.json();
                const created = result?.post || result;
                // Normalize to client shape
                const normalized = {
                    id: created.id,
                    content: created.content || created.title || '',
                    image: created.image || created.mediaUrl || '',
                    createdAt: created.createdAt || new Date().toISOString(),
                    likes: created.likes || 0,
                    comments: created.comments || 0,
                    author: {
                        name: created.username || username || 'User',
                        id: created.userId || uid || 'unknown'
                    },
                    category: created.category || 'Personal Experience'
                };
                this.posts.unshift(normalized);
                // Re-apply filters and render to include new post
                this.applyFiltersAndRender();
                
                // Close modal
                if (window.modalSystem) {
                    window.modalSystem.closeModal();
                }
                
                // Show success message
                this.showSuccessMessage('Your story has been shared!');
                
            } else {
                const errData = await response.json().catch(() => null);
                throw new Error(errData?.error || 'Failed to create post');
            }
            
        } catch (error) {
            console.error('Error creating post:', error);
            alert(`Failed to share your story. ${error?.message || 'Please try again.'}`);
        }
    }

    handlePostAction(event) {
        const button = event.target.closest('.post-action');
        const action = button.dataset.action;
        const postId = button.dataset.postId;
        
        switch (action) {
            case 'like':
                this.toggleLike(postId, button);
                break;
            case 'comment':
                this.showComments(postId);
                break;
            case 'share':
                this.sharePost(postId);
                break;
        }
    }

    async toggleLike(postId, button) {
        // In static mode, allow optimistic like without auth; in API mode, require auth
        const staticMode = !window.location.origin.includes('localhost') && !window.location.origin.includes('127.0.0.1');
        if (!staticMode && !authManager.isAuthenticated()) {
            window.modalSystem?.showAuthModal();
            return;
        }

        try {
            const response = await fetch(`/api/posts/${postId}/like`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ userId: authManager.getCurrentUser()?.uid || 'demo-user' })
            }).catch(() => null);
            
            if (response && response.ok) {
                const result = await response.json();
                const countElement = button.querySelector('.count');
                if (countElement) {
                    countElement.textContent = result.likes;
                }
                
                button.classList.toggle('active', result.liked);
            } else {
                // Optimistic update in static mode
                const countElement = button.querySelector('.count');
                if (countElement) {
                    const current = parseInt(countElement.textContent || '0', 10);
                    countElement.textContent = (current + 1).toString();
                    button.classList.add('active');
                }
            }
        } catch (error) {
            console.error('Error toggling like:', error);
        }
    }

    showComments(postId) {
        console.log('Show comments for post:', postId);
        // TODO: Implement comments modal
    }

    sharePost(postId) {
        if (navigator.share) {
            navigator.share({
                title: 'Check out this story on Naroop',
                url: `${window.location.origin}/post/${postId}`
            });
        } else {
            // Fallback: copy to clipboard
            const url = `${window.location.origin}/post/${postId}`;
            navigator.clipboard.writeText(url).then(() => {
                this.showSuccessMessage('Link copied to clipboard!');
            });
        }
    }

    showSuccessMessage(message) {
        notificationSystem.success(message);
    }
}

// Create and export posts manager instance
const postsManager = new PostsManager();

// Export for use in other modules
export { PostsManager, postsManager };
export default postsManager;
