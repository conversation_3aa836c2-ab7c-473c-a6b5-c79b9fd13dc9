// Scroll Sections for Naroop
// Handles smooth scrolling and section navigation

class ScrollSections {
    constructor() {
        this.sections = [];
        this.currentSection = 0;
        this.isScrolling = false;
        this.init();
    }

    init() {
        this.findSections();
        this.setupEventListeners();
        this.updateActiveSection();
    }

    findSections() {
        this.sections = Array.from(document.querySelectorAll('.section, section'));
        console.log(`Found ${this.sections.length} sections`);
    }

    setupEventListeners() {
        // Wheel event for desktop
        document.addEventListener('wheel', (e) => {
            if (this.isScrolling) return;
            
            if (e.deltaY > 0) {
                this.nextSection();
            } else {
                this.previousSection();
            }
        }, { passive: false });

        // Touch events for mobile
        let touchStartY = 0;
        let touchEndY = 0;

        document.addEventListener('touchstart', (e) => {
            touchStartY = e.changedTouches[0].screenY;
        });

        document.addEventListener('touchend', (e) => {
            touchEndY = e.changedTouches[0].screenY;
            this.handleSwipe(touchStartY, touchEndY);
        });

        // Keyboard navigation
        document.addEventListener('keydown', (e) => {
            if (this.isScrolling) return;
            
            switch (e.key) {
                case 'ArrowDown':
                case 'PageDown':
                    e.preventDefault();
                    this.nextSection();
                    break;
                case 'ArrowUp':
                case 'PageUp':
                    e.preventDefault();
                    this.previousSection();
                    break;
                case 'Home':
                    e.preventDefault();
                    this.goToSection(0);
                    break;
                case 'End':
                    e.preventDefault();
                    this.goToSection(this.sections.length - 1);
                    break;
            }
        });

        // Resize handler
        window.addEventListener('resize', () => {
            this.updateActiveSection();
        });
    }

    handleSwipe(startY, endY) {
        const threshold = 50;
        const diff = startY - endY;

        if (Math.abs(diff) < threshold) return;

        if (diff > 0) {
            this.nextSection();
        } else {
            this.previousSection();
        }
    }

    nextSection() {
        if (this.currentSection < this.sections.length - 1) {
            this.goToSection(this.currentSection + 1);
        }
    }

    previousSection() {
        if (this.currentSection > 0) {
            this.goToSection(this.currentSection - 1);
        }
    }

    goToSection(index) {
        if (index < 0 || index >= this.sections.length || this.isScrolling) return;

        this.isScrolling = true;
        this.currentSection = index;
        const section = this.sections[index];

        section.scrollIntoView({
            behavior: 'smooth',
            block: 'start'
        });

        // Reset scrolling flag after animation
        setTimeout(() => {
            this.isScrolling = false;
        }, 1000);

        this.updateActiveSection();
    }

    updateActiveSection() {
        // Remove active class from all sections
        this.sections.forEach(section => {
            section.classList.remove('active-section');
        });

        // Add active class to current section
        if (this.sections[this.currentSection]) {
            this.sections[this.currentSection].classList.add('active-section');
        }

        // Update navigation indicators if they exist
        this.updateNavigationIndicators();
    }

    updateNavigationIndicators() {
        const indicators = document.querySelectorAll('.section-indicator');
        indicators.forEach((indicator, index) => {
            if (index === this.currentSection) {
                indicator.classList.add('active');
            } else {
                indicator.classList.remove('active');
            }
        });
    }

    // Public method to go to specific section by ID
    goToSectionById(sectionId) {
        const section = document.getElementById(sectionId);
        if (section) {
            const index = this.sections.indexOf(section);
            if (index !== -1) {
                this.goToSection(index);
            }
        }
    }

    // Public method to get current section
    getCurrentSection() {
        return this.currentSection;
    }

    // Public method to get total sections
    getTotalSections() {
        return this.sections.length;
    }
}

// Initialize scroll sections if sections exist
document.addEventListener('DOMContentLoaded', () => {
    const sections = document.querySelectorAll('.section, section');
    if (sections.length > 1) {
        window.scrollSections = new ScrollSections();
    }
});

// Export for use in other modules
window.ScrollSections = ScrollSections;
