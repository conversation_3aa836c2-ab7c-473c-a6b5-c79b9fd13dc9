// Enhanced Stories System for Naroop - Instagram-like Experience

class StoriesManager {
    constructor() {
        this.stories = [];
        this.currentStoryIndex = 0;
        this.isViewingStory = false;
        this.storyViewerElement = null;
        this.init();
    }

    init() {
        this.setupStoryInteractions();
        this.createStoryViewer();
        this.setupKeyboardControls();
        this.loadStoriesData();
    }

    setupStoryInteractions() {
        // Add click handlers to story items
        document.querySelectorAll('.story-item').forEach((item, index) => {
            item.addEventListener('click', () => {
                if (index === 0) {
                    this.openCreateStoryModal();
                } else {
                    this.openStoryViewer(index - 1);
                }
            });

            // Add hover effects
            item.addEventListener('mouseenter', () => {
                this.previewStory(index);
            });

            item.addEventListener('mouseleave', () => {
                this.hideStoryPreview();
            });
        });
    }

    createStoryViewer() {
        // Create story viewer modal
        const viewer = document.createElement('div');
        viewer.className = 'story-viewer';
        viewer.innerHTML = `
            <div class="story-viewer-overlay"></div>
            <div class="story-viewer-content">
                <div class="story-viewer-header">
                    <div class="story-progress-bars"></div>
                    <div class="story-user-info">
                        <div class="story-user-avatar"></div>
                        <div class="story-user-details">
                            <span class="story-username"></span>
                            <span class="story-timestamp"></span>
                        </div>
                    </div>
                    <button class="story-close-btn" aria-label="Close story">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <line x1="18" y1="6" x2="6" y2="18"></line>
                            <line x1="6" y1="6" x2="18" y2="18"></line>
                        </svg>
                    </button>
                </div>
                <div class="story-viewer-media">
                    <div class="story-content"></div>
                    <div class="story-navigation">
                        <button class="story-nav-btn story-prev" aria-label="Previous story">
                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <polyline points="15,18 9,12 15,6"></polyline>
                            </svg>
                        </button>
                        <button class="story-nav-btn story-next" aria-label="Next story">
                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <polyline points="9,18 15,12 9,6"></polyline>
                            </svg>
                        </button>
                    </div>
                </div>
                <div class="story-viewer-actions">
                    <div class="story-reaction-bar">
                        <button class="story-reaction-btn" data-reaction="❤️">❤️</button>
                        <button class="story-reaction-btn" data-reaction="😍">😍</button>
                        <button class="story-reaction-btn" data-reaction="🔥">🔥</button>
                        <button class="story-reaction-btn" data-reaction="👏">👏</button>
                        <button class="story-reaction-btn" data-reaction="💯">💯</button>
                    </div>
                    <div class="story-input-area">
                        <input type="text" placeholder="Reply to story..." class="story-reply-input">
                        <button class="story-send-btn">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <line x1="22" y1="2" x2="11" y2="13"></line>
                                <polygon points="22,2 15,22 11,13 2,9 22,2"></polygon>
                            </svg>
                        </button>
                    </div>
                </div>
            </div>
        `;

        document.body.appendChild(viewer);
        this.storyViewerElement = viewer;

        // Setup viewer event listeners
        this.setupViewerControls();
    }

    setupViewerControls() {
        const viewer = this.storyViewerElement;
        
        // Close button
        viewer.querySelector('.story-close-btn').addEventListener('click', () => {
            this.closeStoryViewer();
        });

        // Overlay click to close
        viewer.querySelector('.story-viewer-overlay').addEventListener('click', () => {
            this.closeStoryViewer();
        });

        // Navigation buttons
        viewer.querySelector('.story-prev').addEventListener('click', () => {
            this.previousStory();
        });

        viewer.querySelector('.story-next').addEventListener('click', () => {
            this.nextStory();
        });

        // Reaction buttons
        viewer.querySelectorAll('.story-reaction-btn').forEach(btn => {
            btn.addEventListener('click', () => {
                this.sendReaction(btn.dataset.reaction);
            });
        });

        // Reply functionality
        const replyInput = viewer.querySelector('.story-reply-input');
        const sendBtn = viewer.querySelector('.story-send-btn');
        
        sendBtn.addEventListener('click', () => {
            this.sendReply(replyInput.value);
            replyInput.value = '';
        });

        replyInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.sendReply(replyInput.value);
                replyInput.value = '';
            }
        });
    }

    setupKeyboardControls() {
        document.addEventListener('keydown', (e) => {
            if (!this.isViewingStory) return;

            switch(e.key) {
                case 'Escape':
                    this.closeStoryViewer();
                    break;
                case 'ArrowLeft':
                    this.previousStory();
                    break;
                case 'ArrowRight':
                case ' ':
                    e.preventDefault();
                    this.nextStory();
                    break;
            }
        });
    }

    loadStoriesData() {
        // Mock stories data - in real app, this would come from API
        this.stories = [
            {
                id: 1,
                username: 'Maya',
                avatar: '👩🏾',
                timestamp: '2 hours ago',
                content: 'Just graduated from medical school! 🎓',
                type: 'text',
                hasNew: true
            },
            {
                id: 2,
                username: 'John',
                avatar: '👨🏿',
                timestamp: '4 hours ago',
                content: 'Beautiful sunset from my morning run 🌅',
                type: 'text',
                hasNew: true
            },
            {
                id: 3,
                username: 'Sarah',
                avatar: '👩🏽',
                timestamp: '6 hours ago',
                content: 'Cooking my grandmother\'s recipe today ❤️',
                type: 'text',
                hasNew: false
            }
        ];

        this.updateStoryIndicators();
    }

    updateStoryIndicators() {
        const storyItems = document.querySelectorAll('.story-item:not(:first-child)');
        
        storyItems.forEach((item, index) => {
            const story = this.stories[index];
            if (story) {
                if (story.hasNew) {
                    item.classList.add('has-new-story');
                    item.classList.remove('viewed-story');
                } else {
                    item.classList.add('viewed-story');
                    item.classList.remove('has-new-story');
                }
            }
        });
    }

    openStoryViewer(storyIndex) {
        this.currentStoryIndex = storyIndex;
        this.isViewingStory = true;
        
        const viewer = this.storyViewerElement;
        viewer.classList.add('active');
        document.body.style.overflow = 'hidden';
        
        this.displayCurrentStory();
        this.startStoryProgress();
    }

    closeStoryViewer() {
        this.isViewingStory = false;
        const viewer = this.storyViewerElement;
        viewer.classList.remove('active');
        document.body.style.overflow = '';
        
        this.stopStoryProgress();
    }

    displayCurrentStory() {
        const story = this.stories[this.currentStoryIndex];
        if (!story) return;

        const viewer = this.storyViewerElement;
        
        // Update user info
        viewer.querySelector('.story-user-avatar').textContent = story.avatar;
        viewer.querySelector('.story-username').textContent = story.username;
        viewer.querySelector('.story-timestamp').textContent = story.timestamp;
        
        // Update content
        viewer.querySelector('.story-content').innerHTML = `
            <div class="story-text-content">
                <p>${story.content}</p>
            </div>
        `;
        
        // Update progress bars
        this.updateProgressBars();
        
        // Mark as viewed
        story.hasNew = false;
        this.updateStoryIndicators();
    }

    updateProgressBars() {
        const progressContainer = this.storyViewerElement.querySelector('.story-progress-bars');
        progressContainer.innerHTML = '';
        
        this.stories.forEach((_, index) => {
            const bar = document.createElement('div');
            bar.className = 'story-progress-bar';
            if (index < this.currentStoryIndex) {
                bar.classList.add('completed');
            } else if (index === this.currentStoryIndex) {
                bar.classList.add('active');
            }
            progressContainer.appendChild(bar);
        });
    }

    startStoryProgress() {
        const activeBar = this.storyViewerElement.querySelector('.story-progress-bar.active');
        if (activeBar) {
            activeBar.style.animation = 'storyProgress 5s linear forwards';
            
            setTimeout(() => {
                if (this.isViewingStory) {
                    this.nextStory();
                }
            }, 5000);
        }
    }

    stopStoryProgress() {
        const activeBar = this.storyViewerElement.querySelector('.story-progress-bar.active');
        if (activeBar) {
            activeBar.style.animation = 'none';
        }
    }

    nextStory() {
        if (this.currentStoryIndex < this.stories.length - 1) {
            this.currentStoryIndex++;
            this.displayCurrentStory();
            this.startStoryProgress();
        } else {
            this.closeStoryViewer();
        }
    }

    previousStory() {
        if (this.currentStoryIndex > 0) {
            this.currentStoryIndex--;
            this.displayCurrentStory();
            this.startStoryProgress();
        }
    }

    sendReaction(reaction) {
        // Animate reaction
        const reactionEl = document.createElement('div');
        reactionEl.className = 'story-reaction-animation';
        reactionEl.textContent = reaction;
        reactionEl.style.cssText = `
            position: absolute;
            font-size: 2rem;
            pointer-events: none;
            z-index: 1000;
            animation: reactionFloat 2s ease-out forwards;
        `;
        
        const viewer = this.storyViewerElement.querySelector('.story-content');
        viewer.appendChild(reactionEl);
        
        setTimeout(() => {
            reactionEl.remove();
        }, 2000);
        
        console.log(`Sent reaction: ${reaction} to story ${this.currentStoryIndex}`);
    }

    sendReply(message) {
        if (message.trim()) {
            console.log(`Sent reply: "${message}" to story ${this.currentStoryIndex}`);
            // In real app, this would send to backend
        }
    }

    openCreateStoryModal() {
        console.log('Opening create story modal...');
        // This would open a modal for creating new stories
    }

    previewStory(index) {
        // Show story preview on hover
        if (index > 0 && this.stories[index - 1]) {
            // Could show a small preview tooltip
        }
    }

    hideStoryPreview() {
        // Hide story preview
    }
}

// Initialize stories when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.storiesManager = new StoriesManager();
});

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = StoriesManager;
}
