<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Naroop - Connect & Share Your Story</title>

    <!-- External CSS Files -->
    <link rel="stylesheet" href="/public/css/variables.css?v=3">
    <link rel="stylesheet" href="/public/css/main.css?v=3">
    <link rel="stylesheet" href="/public/css/modern-animations.css?v=1">
    <link rel="stylesheet" href="/public/css/responsive.css">
    <link rel="stylesheet" href="/public/css/forms.css">
    <link rel="stylesheet" href="/public/css/notifications.css">
    <link rel="stylesheet" href="/public/css/layout-improvements.css?v=1">
    <link rel="stylesheet" href="/public/css/landing-specific.css?v=1">
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="logo">Naroop</div>
        </div>
    </header>

    <!-- Hero Section -->
    <section class="hero">
        <div class="hero-content">
            <h1 class="hero-title">Connect & Share</h1>
            <p class="hero-subtitle">A place where our community comes together to share positive experiences, inspire each other, and build meaningful connections.</p>
            <div class="hero-buttons fade-in-up">
                <button class="hero-btn btn-base btn-primary btn-lg hover-lift focus-ring" type="button" data-auth-mode="signup">Join Our Community</button>
                <button class="hero-btn btn-base btn-secondary btn-lg hover-lift focus-ring" type="button" data-auth-mode="signin">Sign In</button>
            </div>
        </div>
    </section>

    <!-- Authentication Modal -->
    <div id="authModal" class="auth-modal">
        <div class="auth-modal-content">
            <div class="auth-modal-header">
                <h3 id="authModalTitle">Welcome to Naroop</h3>
                <button id="closeAuthModal" class="close-btn">&times;</button>
            </div>
            <div class="auth-modal-body">
                <!-- Sign In Form -->
                <div id="signInForm" class="auth-form">
                    <div class="form-group">
                        <label for="signInEmail">Email</label>
                        <input type="email" id="signInEmail" placeholder="Enter your email" required>
                        <div class="field-error" id="signInEmailError"></div>
                        <div class="field-success" id="signInEmailSuccess"></div>
                    </div>
                    <div class="form-group">
                        <label for="signInPassword">Password</label>
                        <input type="password" id="signInPassword" placeholder="Enter your password" required>
                        <div class="field-error" id="signInPasswordError"></div>
                        <div class="field-success" id="signInPasswordSuccess"></div>
                    </div>
                    <button id="signInBtn" class="auth-btn" type="button">Sign In</button>
                    <p class="auth-switch">Don't have an account? <a href="#" id="showSignUpForm">Join our community</a></p>
                </div>

                <!-- Sign Up Form -->
                <div id="signUpForm" class="auth-form" style="display: none;">
                    <div class="form-group">
                        <label for="signUpUsername">Username</label>
                        <input type="text" id="signUpUsername" placeholder="Choose a username" required>
                        <div class="field-error" id="signUpUsernameError"></div>
                        <div class="field-success" id="signUpUsernameSuccess"></div>
                    </div>
                    <div class="form-group">
                        <label for="signUpEmail">Email</label>
                        <input type="email" id="signUpEmail" placeholder="Enter your email" required>
                        <div class="field-error" id="signUpEmailError"></div>
                        <div class="field-success" id="signUpEmailSuccess"></div>
                    </div>
                    <div class="form-group">
                        <label for="signUpPassword">Password</label>
                        <input type="password" id="signUpPassword" placeholder="Create a password" required>
                        <div class="field-error" id="signUpPasswordError"></div>
                        <div class="field-success" id="signUpPasswordSuccess"></div>
                    </div>
                    <button id="signUpBtn" class="auth-btn" type="button">Join Our Community</button>
                    <p class="auth-switch">Already have an account? <a href="#" id="showSignInForm">Sign in</a></p>
                </div>

                <div id="authError" class="auth-error" style="display: none;"></div>
                <div id="authLoading" class="auth-loading" style="display: none;">
                    <div class="loading-spinner"></div>
                    <p>Please wait...</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Ultra-early, resilient fallback so buttons always work even if modules fail to load -->
    <script>
        (function () {
            if (window.__openAuth) return; // respect module-defined version if already present
            window.__openAuth = function (mode) {
                try {
                    var authModal = document.getElementById('authModal');
                    if (!authModal) return;
                    // Ensure modal is visible even when CSS sets opacity/visibility defaults
                    authModal.style.display = 'flex';
                    authModal.classList.add('show');
                    var signInForm = document.getElementById('signInForm');
                    var signUpForm = document.getElementById('signUpForm');
                    if (mode === 'signup') {
                        if (signInForm) signInForm.style.display = 'none';
                        if (signUpForm) signUpForm.style.display = 'block';
                    } else {
                        if (signInForm) signInForm.style.display = 'block';
                        if (signUpForm) signUpForm.style.display = 'none';
                    }
                } catch (_) { /* no-op */ }
            };

            // Minimal wiring so data-auth-mode buttons still open the modal without modules
            document.addEventListener('DOMContentLoaded', function () {
                try {
                    document.querySelectorAll('[data-auth-mode]').forEach(function(btn){
                        btn.addEventListener('click', function(){
                            var mode = this.getAttribute('data-auth-mode') || 'signin';
                            window.__openAuth(mode);
                        });
                    });
                } catch (_) { /* no-op */ }
            });
        })();
    </script>

    <script type="module">
        // Use the existing authentication stack
        import firebaseAuth from '/public/js/firebase-config.js';
        import { authManager } from '/public/js/authentication.js';

        // Redirect if already authenticated
        document.addEventListener('DOMContentLoaded', async () => {
            try {
                if (firebaseAuth.isAuthenticated()) {
                    window.location.href = '/app';
                    return;
                }

                // Also listen for auth state changes (e.g., demo mode or delayed init)
                const checkInterval = setInterval(() => {
                    if (firebaseAuth.isAuthenticated && firebaseAuth.isAuthenticated()) {
                        clearInterval(checkInterval);
                        window.location.href = '/app';
                    }
                }, 500);
                setTimeout(() => clearInterval(checkInterval), 10000);
            } catch (e) {
                console.warn('Auth pre-check failed:', e);
            }
        });

        // Smooth scroll for logo click
        document.querySelector('.logo').addEventListener('click', function() {
            window.scrollTo({ top: 0, behavior: 'smooth' });
        });

        // Add scroll effect to header
        window.addEventListener('scroll', function() {
            const header = document.querySelector('.header');
            if (window.scrollY > 50) {
                header.style.background = 'rgba(255, 255, 255, 0.98)';
                header.style.boxShadow = '0 2px 20px rgba(0, 0, 0, 0.1)';
            } else {
                header.style.background = 'rgba(255, 255, 255, 0.95)';
                header.style.boxShadow = 'none';
            }
        });

        // Modal functionality
        const authModal = document.getElementById('authModal');
        const closeAuthModal = document.getElementById('closeAuthModal');
        const signInForm = document.getElementById('signInForm');
        const signUpForm = document.getElementById('signUpForm');
        const showSignUpForm = document.getElementById('showSignUpForm');
        const showSignInForm = document.getElementById('showSignInForm');
        const authModalTitle = document.getElementById('authModalTitle');
        const authError = document.getElementById('authError');
        const authLoading = document.getElementById('authLoading');

    // Global open helper
    window.__openAuth = (mode) => openModal(mode);

        // Button click handlers using data-auth-mode attribute
        document.querySelectorAll('[data-auth-mode]').forEach(btn => {
            btn.addEventListener('click', function() {
                const mode = this.getAttribute('data-auth-mode') || 'signin';
                openModal(mode);
            }, { passive: true });
        });

        function openModal(mode) {
            // Make modal visible (CSS requires .show for opacity/visibility)
            authModal.style.display = 'flex';
            authModal.classList.add('show');
            hideError();
            hideLoading();

            if (mode === 'signup') {
                showSignUpFormView();
            } else {
                showSignInFormView();
            }
        }

        function closeModal() {
            // Hide modal and remove visibility class
            authModal.classList.remove('show');
            authModal.style.display = 'none';
            clearForms();
        }

        function showSignInFormView() {
            signInForm.style.display = 'block';
            signUpForm.style.display = 'none';
            authModalTitle.textContent = 'Welcome Back';
        }

        function showSignUpFormView() {
            signInForm.style.display = 'none';
            signUpForm.style.display = 'block';
            authModalTitle.textContent = 'Join Our Community';
        }

        function showError(message) {
            authError.textContent = message;
            authError.style.display = 'block';
        }

        function hideError() {
            authError.style.display = 'none';
        }

        function showLoading() {
            authLoading.style.display = 'block';
        }

        function hideLoading() {
            authLoading.style.display = 'none';
        }

        function clearForms() {
            document.getElementById('signInEmail').value = '';
            document.getElementById('signInPassword').value = '';
            document.getElementById('signUpUsername').value = '';
            document.getElementById('signUpEmail').value = '';
            document.getElementById('signUpPassword').value = '';
            hideError();
            hideLoading();
            clearFieldValidation();
        }

        function clearFieldValidation() {
            // Clear all field validation states
            const inputs = document.querySelectorAll('.form-group input');
            const errors = document.querySelectorAll('.field-error');
            const successes = document.querySelectorAll('.field-success');

            inputs.forEach(input => {
                input.classList.remove('error', 'success');
            });

            errors.forEach(error => {
                error.style.display = 'none';
                error.textContent = '';
            });

            successes.forEach(success => {
                success.style.display = 'none';
                success.textContent = '';
            });
        }

        function showFieldError(fieldId, message) {
            const input = document.getElementById(fieldId);
            const errorElement = document.getElementById(fieldId + 'Error');
            const successElement = document.getElementById(fieldId + 'Success');

            input.classList.remove('success');
            input.classList.add('error');

            if (errorElement) {
                errorElement.textContent = message;
                errorElement.style.display = 'block';
            }

            if (successElement) {
                successElement.style.display = 'none';
            }
        }

        function showFieldSuccess(fieldId, message = '') {
            const input = document.getElementById(fieldId);
            const errorElement = document.getElementById(fieldId + 'Error');
            const successElement = document.getElementById(fieldId + 'Success');

            input.classList.remove('error');
            input.classList.add('success');

            if (errorElement) {
                errorElement.style.display = 'none';
            }

            if (successElement && message) {
                successElement.textContent = message;
                successElement.style.display = 'block';
            }
        }

        function clearFieldValidationState(fieldId) {
            const input = document.getElementById(fieldId);
            const errorElement = document.getElementById(fieldId + 'Error');
            const successElement = document.getElementById(fieldId + 'Success');

            input.classList.remove('error', 'success');

            if (errorElement) {
                errorElement.style.display = 'none';
            }

            if (successElement) {
                successElement.style.display = 'none';
            }
        }

        // Event listeners
        closeAuthModal.addEventListener('click', closeModal);
        showSignUpForm.addEventListener('click', (e) => {
            e.preventDefault();
            showSignUpFormView();
        });
        showSignInForm.addEventListener('click', (e) => {
            e.preventDefault();
            showSignInFormView();
        });

        // Close modal when clicking outside
        authModal.addEventListener('click', (e) => {
            if (e.target === authModal) {
                closeModal();
            }
        });

        // Enter key submits active form
        authModal.addEventListener('keydown', (e) => {
            if (e.key === 'Enter') {
                if (signInForm.style.display !== 'none') {
                    document.getElementById('signInBtn').click();
                } else if (signUpForm.style.display !== 'none') {
                    document.getElementById('signUpBtn').click();
                }
            }
        });

        // Lightweight input feedback (no Firebase validators on landing)
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        document.getElementById('signUpEmail').addEventListener('input', function () {
            const email = this.value.trim();
            if (!email) return clearFieldValidationState('signUpEmail');
            emailRegex.test(email) ? showFieldSuccess('signUpEmail', '✓ Valid email') : showFieldError('signUpEmail', 'Please enter a valid email address');
        });
        document.getElementById('signInEmail').addEventListener('input', function () {
            const email = this.value.trim();
            if (!email) return clearFieldValidationState('signInEmail');
            emailRegex.test(email) ? showFieldSuccess('signInEmail', '✓ Valid email') : showFieldError('signInEmail', 'Please enter a valid email address');
        });
        document.getElementById('signUpPassword').addEventListener('input', function () {
            const pwd = this.value;
            if (!pwd) return clearFieldValidationState('signUpPassword');
            pwd.length >= 6 ? showFieldSuccess('signUpPassword', '✓ Looks good') : showFieldError('signUpPassword', 'Password must be at least 6 characters');
        });
        document.getElementById('signInPassword').addEventListener('input', function () {
            const pwd = this.value;
            if (!pwd) return clearFieldValidationState('signInPassword');
            clearFieldValidationState('signInPassword');
        });

        // Sign In functionality
        document.getElementById('signInBtn').addEventListener('click', async function() {
            const email = document.getElementById('signInEmail').value.trim();
            const password = document.getElementById('signInPassword').value;

            // Client-side validation
            if (!email || !password) {
                showError('Please fill in all fields');
                return;
            }

            if (!emailRegex.test(email)) {
                showError('Please enter a valid email address');
                return;
            }

            showLoading();
            hideError();

            try {
                const result = await authManager.signIn(email, password);
                if (result.success) {
                    // Use index.html for broader static-serve compatibility
                    window.location.href = '/index.html';
                } else {
                    showError(result.error || 'Sign in failed');
                }
            } catch (error) {
                console.error('Sign in error:', error);
                showError('An error occurred. Please try again.');
            } finally {
                hideLoading();
            }
        });

        // Sign Up functionality
    document.getElementById('signUpBtn').addEventListener('click', async function() {
            const username = document.getElementById('signUpUsername').value.trim();
            const email = document.getElementById('signUpEmail').value.trim();
            const password = document.getElementById('signUpPassword').value;

            // Client-side validation
            if (!username || !email || !password) {
                showError('Please fill in all fields');
                return;
            }

            // Basic validation
            if (!emailRegex.test(email)) {
                showError('Please enter a valid email address');
                return;
            }
            if (password.length < 6) {
                showError('Password must be at least 6 characters long');
                return;
            }
            if (username.length < 2) {
                showError('Username must be at least 2 characters long');
                return;
            }

            showLoading();
            hideError();

            try {
                // Create account; username will be synced via server on first login
                const result = await authManager.signUp(email, password);
                if (result.success) {
                    // Optionally, create a profile with provided username
                    try {
                        // This will succeed only if server auth is configured; safe to ignore failures in dev/demo
                        const stored = localStorage.getItem('naroop_user');
                        const user = stored ? JSON.parse(stored) : null;
                        if (user) {
                            await fetch('/api/register', {
                                method: 'POST',
                                headers: { 'Content-Type': 'application/json' },
                                body: JSON.stringify({ uid: user.uid, username, email })
                            });
                        }
                    } catch {}
                    window.location.href = '/index.html';
                } else {
                    showError(result.error || 'Sign up failed');
                }
            } catch (error) {
                console.error('Sign up error:', error);
                showError('An error occurred. Please try again.');
            } finally {
                hideLoading();
            }
        });
        // No separate sync needed; firebase-config handles profile sync on auth
    </script>
</body>
</html>