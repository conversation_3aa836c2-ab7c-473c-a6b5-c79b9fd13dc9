{"name": "naroop-social-platform", "version": "1.0.0", "description": "Naroop - A social media platform where Black people share positive stories and celebrate community", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "dev-live": "node dev-server.js", "test": "node server.js", "prod": "node server.js", "logs": "type logs\\naroop.log", "setup": "npm install && node scripts/setup.js", "health": "node -e \"const port = process.env.PORT || 3000; require('http').get(`http://localhost:${port}/health`, res => { let data = ''; res.on('data', chunk => data += chunk); res.on('end', () => console.log(data)); })\"", "cap:copy": "npx cap copy ios", "cap:sync": "npx cap sync ios", "cap:build": "npm run cap:copy && echo iOS project ready for Xcode", "icons:generate": "node scripts/generate-icons.js"}, "dependencies": {"@capacitor/cli": "^6.2.1", "@capacitor/core": "^7.4.2", "@capacitor/ios": "^7.4.2", "body-parser": "^1.20.2", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "express": "^4.18.2", "firebase": "^11.10.0", "firebase-admin": "^13.4.0", "multer": "^1.4.5-lts.1"}, "optionalDependencies": {"compression": "^1.7.4", "dotenv": "^16.3.1", "express-rate-limit": "^7.1.5", "helmet": "^7.1.0"}, "devDependencies": {"chokidar": "^3.5.3", "nodemon": "^3.1.10", "typescript": "^5.9.2", "ws": "^8.14.2"}, "engines": {"node": ">=14.0.0", "npm": ">=6.0.0"}, "keywords": ["social-media", "black-community", "positive-stories", "storytelling", "community", "naroop"], "author": "<PERSON>", "license": "MIT"}