/* Advanced Features - 2025 Social Media Design */

/* Enhanced Post Creation Modal */
.post-creation-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: var(--z-modal);
    opacity: 0;
    visibility: hidden;
    transition: all var(--transition-normal);
}

.post-creation-modal.active {
    opacity: 1;
    visibility: visible;
}

.post-creation-content {
    background: var(--glass-background);
    backdrop-filter: var(--glass-blur);
    -webkit-backdrop-filter: var(--glass-blur);
    border: 1px solid var(--glass-border);
    border-radius: var(--border-radius-xl);
    box-shadow: var(--glass-shadow);
    width: 90%;
    max-width: 600px;
    max-height: 80vh;
    overflow-y: auto;
    padding: 24px;
    transform: scale(0.9) translateY(20px);
    transition: all var(--transition-normal);
}

.post-creation-modal.active .post-creation-content {
    transform: scale(1) translateY(0);
}

.post-creation-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 24px;
    padding-bottom: 16px;
    border-bottom: 1px solid var(--border-color);
}

.post-creation-header h3 {
    margin: 0;
    color: var(--text-primary);
    font-weight: var(--font-weight-semibold);
}

.close-modal-btn {
    background: none;
    border: none;
    color: var(--text-secondary);
    font-size: 24px;
    cursor: pointer;
    padding: 4px;
    border-radius: var(--border-radius-md);
    transition: all var(--transition-normal);
}

.close-modal-btn:hover {
    background: rgba(255, 255, 255, 0.1);
    color: var(--text-primary);
}

/* Rich Text Editor */
.rich-text-editor {
    background: var(--surface-color);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-lg);
    min-height: 120px;
    padding: 16px;
    color: var(--text-primary);
    font-family: var(--font-family-primary);
    font-size: var(--font-size-base);
    line-height: var(--line-height-normal);
    resize: vertical;
    transition: all var(--transition-normal);
}

.rich-text-editor:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(255, 112, 67, 0.1);
}

.rich-text-editor::placeholder {
    color: var(--text-muted);
}

/* Media Upload Area */
.media-upload-area {
    border: 2px dashed var(--border-color);
    border-radius: var(--border-radius-lg);
    padding: 32px;
    text-align: center;
    margin: 16px 0;
    transition: all var(--transition-normal);
    cursor: pointer;
}

.media-upload-area:hover {
    border-color: var(--primary-color);
    background: rgba(255, 112, 67, 0.05);
}

.media-upload-area.dragover {
    border-color: var(--accent-color);
    background: rgba(38, 198, 218, 0.1);
    transform: scale(1.02);
}

.upload-icon {
    width: 48px;
    height: 48px;
    margin: 0 auto 16px;
    color: var(--text-secondary);
}

.upload-text {
    color: var(--text-secondary);
    margin-bottom: 8px;
}

.upload-subtext {
    color: var(--text-muted);
    font-size: var(--font-size-sm);
}

/* Post Options */
.post-options {
    display: flex;
    gap: 16px;
    margin: 16px 0;
    flex-wrap: wrap;
}

.post-option {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 12px;
    background: var(--surface-color);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-lg);
    cursor: pointer;
    transition: all var(--transition-normal);
}

.post-option:hover {
    background: var(--glass-background);
    border-color: var(--primary-color);
}

.post-option.active {
    background: rgba(255, 112, 67, 0.1);
    border-color: var(--primary-color);
    color: var(--primary-color);
}

.post-option svg {
    width: 16px;
    height: 16px;
}

/* Hashtag Suggestions */
.hashtag-suggestions {
    position: relative;
}

.hashtag-dropdown {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: var(--glass-background);
    backdrop-filter: var(--glass-blur);
    -webkit-backdrop-filter: var(--glass-blur);
    border: 1px solid var(--glass-border);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--glass-shadow);
    max-height: 200px;
    overflow-y: auto;
    z-index: 1000;
    display: none;
}

.hashtag-dropdown.active {
    display: block;
    animation: fadeInUp 0.2s ease-out;
}

.hashtag-item {
    padding: 12px 16px;
    cursor: pointer;
    transition: background-color var(--transition-fast);
    display: flex;
    align-items: center;
    gap: 8px;
}

.hashtag-item:hover {
    background: rgba(255, 112, 67, 0.1);
}

.hashtag-item .hashtag-name {
    color: var(--primary-color);
    font-weight: var(--font-weight-medium);
}

.hashtag-item .hashtag-count {
    color: var(--text-muted);
    font-size: var(--font-size-sm);
    margin-left: auto;
}

/* Enhanced Feed Cards - Modern spacing system */
.feed-card {
    background: var(--glass-background);
    backdrop-filter: var(--glass-blur);
    -webkit-backdrop-filter: var(--glass-blur);
    border: 1px solid var(--glass-border);
    border-radius: var(--border-radius-xl);
    box-shadow: var(--glass-shadow);
    overflow: hidden;
    transition: all var(--transition-normal);
    position: relative;
    z-index: var(--z-base);
    /* Remove margin-bottom - spacing handled by parent container */
}

/* Feed Cards Container - Proper spacing system */
#postsContainer {
    display: grid;
    gap: var(--gap-card);
    width: 100%;
}

.feed-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--glass-shadow), var(--shadow-glow);
    /* Ensure hover doesn't cause layout shift */
    z-index: var(--z-dropdown);
}

.feed-card-header {
    padding: 16px 20px;
    display: flex;
    align-items: center;
    gap: 12px;
}

.feed-card-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: var(--gradient-primary);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: var(--font-weight-semibold);
}

.feed-card-user-info {
    flex: 1;
}

.feed-card-username {
    color: var(--text-primary);
    font-weight: var(--font-weight-semibold);
    margin: 0 0 4px 0;
}

.feed-card-timestamp {
    color: var(--text-muted);
    font-size: var(--font-size-sm);
    margin: 0;
}

.feed-card-content {
    padding: 0 20px 16px;
    color: var(--text-secondary);
    line-height: var(--line-height-relaxed);
}

.feed-card-actions {
    padding: 12px 20px;
    border-top: 1px solid var(--border-color);
    display: flex;
    gap: 16px;
}

.feed-action-btn {
    display: flex;
    align-items: center;
    gap: 8px;
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    padding: 8px 12px;
    border-radius: var(--border-radius-lg);
    transition: all var(--transition-normal);
    font-size: var(--font-size-sm);
}

.feed-action-btn:hover {
    background: rgba(255, 255, 255, 0.1);
    color: var(--text-primary);
}

.feed-action-btn.liked {
    color: var(--error-color);
}

.feed-action-btn svg {
    width: 16px;
    height: 16px;
}

/* Floating Action Button */
.fab {
    position: fixed;
    bottom: 80px;
    right: 24px;
    width: 56px;
    height: 56px;
    background: var(--gradient-primary);
    border: none;
    border-radius: 50%;
    box-shadow: var(--shadow-lg);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all var(--transition-normal);
    z-index: var(--z-fixed);
}

.fab:hover {
    transform: scale(1.1);
    box-shadow: var(--shadow-xl), var(--shadow-glow);
}

.fab svg {
    width: 24px;
    height: 24px;
    stroke: white;
}

@media (max-width: 768px) {
    .fab {
        bottom: 100px;
        right: 16px;
    }
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInFromRight {
    from {
        opacity: 0;
        transform: translateX(20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes heartBeat {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.2); }
}

.heart-animation {
    animation: heartBeat 0.6s ease-in-out;
}
