/* Modern Dark Modal Styles */

.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(15, 23, 42, 0.8); /* slate-900 with opacity */
    backdrop-filter: blur(8px);
    display: none; /* Hidden by default */
    align-items: center;
    justify-content: center;
    z-index: 1000;
    opacity: 0;
    transition: opacity 0.3s ease-in-out;
}

.modal-overlay.active {
    display: flex;
    opacity: 1;
}

.modal-content {
    background-color: #1E293B; /* slate-800 */
    border-radius: 0.75rem;
    border: 1px solid #334155; /* slate-700 */
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    width: 90%;
    max-width: 500px;
    max-height: 90vh;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    transform: scale(0.95);
    transition: transform 0.3s ease-in-out;
}

.modal-overlay.active .modal-content {
    transform: scale(1);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 1.5rem;
    border-bottom: 1px solid #334155; /* slate-700 */
}

.modal-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: #F8FAFC; /* slate-50 */
}

.modal-close-btn {
    background: none;
    border: none;
    color: #94A3B8; /* slate-400 */
    font-size: 1.5rem;
    cursor: pointer;
    transition: color 0.2s;
}

.modal-close-btn:hover {
    color: #F8FAFC; /* slate-50 */
}

.modal-body {
    padding: 1.5rem;
    color: #CBD5E1; /* slate-300 */
}

.modal-footer {
    display: flex;
    justify-content: flex-end;
    padding: 1rem 1.5rem;
    border-top: 1px solid #334155; /* slate-700 */
    background-color: #0F172A; /* slate-900 */
    border-bottom-left-radius: 0.75rem;
    border-bottom-right-radius: 0.75rem;
}

/* Example content styling */
.notification-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.notification-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 0.75rem 0;
    border-bottom: 1px solid #334155; /* slate-700 */
}

.notification-item:last-child {
    border-bottom: none;
}

.notification-item .icon {
    color: #818CF8; /* indigo-400 */
}

.profile-menu {
    list-style: none;
    padding: 0;
    margin: 0;
}

.profile-menu-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem 1rem;
    border-radius: 0.5rem;
    cursor: pointer;
    transition: background-color 0.2s;
}

.profile-menu-item:hover {
    background-color: #334155; /* slate-700 */
}
