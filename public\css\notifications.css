/* Naroop Notifications Styles - shares card-base visuals */

.notification-container {
    position: fixed;
    top: var(--header-height);
    right: var(--spacing-md);
    z-index: var(--z-popover);
    max-width: 300px;
}

.notification {
    /* base visuals inherited via .notification included in main.css card-base group */
    border-radius: var(--border-radius-md);
    padding: var(--spacing-md);
    margin-bottom: var(--spacing-sm);
    box-shadow: var(--shadow-lg);
    animation: slideInRight 0.3s ease-out;
}

.notification.success {
    border-left: 4px solid var(--success-color);
}

.notification.error {
    border-left: 4px solid var(--error-color);
}

.notification.warning {
    border-left: 4px solid var(--warning-color);
}

.notification.info {
    border-left: 4px solid var(--info-color);
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}
