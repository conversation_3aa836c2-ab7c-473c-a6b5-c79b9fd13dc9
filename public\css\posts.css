/* Naroop Posts Styles - unified with core card & button system */

.post { padding: var(--spacing-lg); position: relative; z-index: var(--z-base); }

.post-header {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-md);
}

.post-avatar {
    width: 40px;
    height: 40px;
    border-radius: var(--border-radius-full);
    background: var(--gradient-primary);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: var(--font-weight-bold);
}

.post-meta {
    flex: 1;
}

.post-author {
    font-weight: var(--font-weight-semibold);
    color: var(--text-primary);
}

.post-time {
    font-size: var(--font-size-sm);
    color: var(--text-muted);
}

.post-content {
    margin-bottom: var(--spacing-md);
    line-height: var(--line-height-relaxed);
}

.post-actions {
    display: flex;
    gap: var(--spacing-md);
    padding-top: var(--spacing-md);
    border-top: 1px solid var(--border-color);
}

.post-action { /* align with feed-action-btn styling */
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-xs) var(--spacing-sm);
    border: none;
    background: none;
    color: var(--text-muted);
    cursor: pointer;
    border-radius: var(--border-radius-sm);
    transition: all var(--transition-fast);
}

.post-action:hover {
    background: var(--surface-color);
    color: var(--text-primary);
}

.post-action.active {
    color: var(--primary-color);
}
