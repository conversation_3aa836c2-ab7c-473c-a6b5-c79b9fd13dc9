/* Modern Stories Section - Matching React Snippet Design */

/* Main stories section container */
.stories-section-modern {
    display: flex;
    flex-direction: column;
    background: #0f172a; /* slate-900 equivalent */
    border-radius: 0.5rem; /* 8px */
    padding: 1.5rem; /* 24px */
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
    color: white;
    min-height: 300px;
    width: 100%;
    max-width: 56rem; /* 896px - max-w-4xl */
    margin: 0 auto 1.5rem auto;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    position: relative;
    z-index: 1;
}

/* Header section with title and "View All" button */
.stories-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem; /* 16px */
}

.stories-title {
    font-size: 1.25rem; /* 20px */
    font-weight: 700;
    letter-spacing: -0.025em;
    margin: 0;
    color: white;
}

.stories-view-all-btn {
    font-size: 0.875rem; /* 14px */
    padding: 0.5rem 1rem; /* 8px 16px */
    background: #334155; /* slate-700 */
    color: white;
    border: none;
    border-radius: 9999px; /* fully rounded */
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.stories-view-all-btn:hover {
    background: #475569; /* slate-600 */
}

/* Main stories feed container with horizontal scrolling */
.stories-feed-container {
    position: relative;
}

.stories-scroll-container {
    display: flex;
    gap: 1.5rem; /* 24px */
    overflow-x: auto;
    scroll-behavior: smooth;
    padding: 0.5rem; /* 8px */
    margin: -0.5rem; /* -8px */
    /* Hide scrollbar */
    -ms-overflow-style: none; /* Internet Explorer 10+ */
    scrollbar-width: none; /* Firefox */
}

.stories-scroll-container::-webkit-scrollbar {
    display: none; /* Safari and Chrome */
}

/* Individual story item */
.story-item-modern {
    flex-shrink: 0;
    display: flex;
    flex-direction: column;
    align-items: center;
    cursor: pointer;
    transition: all 0.2s ease;
}

.story-item-modern:hover .story-name-modern {
    color: white;
}

/* Story avatar container */
.story-avatar-modern {
    position: relative;
    width: 4rem; /* 64px */
    height: 4rem; /* 64px */
    border-radius: 50%;
    overflow: hidden;
    transition: all 0.2s ease;
    border: 2px solid transparent;
}

/* Add story specific styling */
.add-story-avatar {
    background: #1e293b; /* slate-800 */
    display: flex;
    align-items: center;
    justify-content: center;
    border: 2px solid transparent;
}

.story-item-modern:hover .add-story-avatar {
    border-color: #f97316; /* orange-500 */
}

.add-story-icon {
    height: 2rem; /* 32px */
    width: 2rem; /* 32px */
    color: #f97316; /* orange-500 */
}

/* Story image */
.story-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.2s ease;
}

.story-item-modern:hover .story-image {
    transform: scale(1.1);
}

/* Story avatar rings for active stories */
.has-story {
    border: 2px solid #0ea5e9; /* sky-500 */
    border-offset: 2px;
}

.story-item-modern:hover .has-story {
    border-color: #0284c7; /* sky-600 */
}

.no-story {
    border: 2px solid #475569; /* slate-600 */
    border-offset: 2px;
}

.story-item-modern:hover .no-story {
    border-color: #64748b; /* slate-500 */
}

/* Story name text */
.story-name-modern {
    margin-top: 0.5rem; /* 8px */
    font-size: 0.75rem; /* 12px */
    text-align: center;
    color: #94a3b8; /* slate-400 */
    transition: color 0.2s ease;
    margin-bottom: 0;
}

/* Responsive design for mobile */
@media (max-width: 575.98px) {
    .stories-title {
        font-size: 1.125rem; /* 18px */
    }
    
    .story-avatar-modern {
        width: 3.5rem; /* 56px */
        height: 3.5rem; /* 56px */
    }
    
    .add-story-icon {
        height: 1.75rem; /* 28px */
        width: 1.75rem; /* 28px */
    }
    
    .story-name-modern {
        font-size: 0.6875rem; /* 11px */
    }
    
    .stories-scroll-container {
        gap: 1rem; /* 16px */
    }
}

/* Responsive design for tablet */
@media (min-width: 576px) and (max-width: 767.98px) {
    .story-avatar-modern {
        width: 4.5rem; /* 72px */
        height: 4.5rem; /* 72px */
    }
}

/* Desktop responsive design */
@media (min-width: 768px) {
    .stories-title {
        font-size: 1.5rem; /* 24px */
    }
    
    .story-avatar-modern {
        width: 5rem; /* 80px */
        height: 5rem; /* 80px */
    }
    
    .add-story-icon {
        height: 2rem; /* 32px */
        width: 2rem; /* 32px */
    }
    
    .story-name-modern {
        font-size: 0.875rem; /* 14px */
    }
}

/* Focus states for accessibility */
.story-item-modern:focus {
    outline: 2px solid #f97316; /* orange-500 */
    outline-offset: 2px;
    border-radius: 0.5rem;
}

.stories-view-all-btn:focus {
    outline: 2px solid #f97316; /* orange-500 */
    outline-offset: 2px;
}

/* Animation for smooth interactions */
.story-item-modern {
    transform: translateZ(0);
    will-change: transform;
}

.stories-scroll-container {
    transform: translateZ(0);
    will-change: scroll-position;
}

/* Performance optimizations */
.stories-section-modern {
    contain: layout style paint;
}
