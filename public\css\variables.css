/* Naroop CSS Variables */
/* Color Palette */
:root {
    /* Primary Colors - 2025 Enhanced Palette */
    --primary-color: #FF7043;
    --primary-light: #FF8A65;
    --primary-dark: #F4511E;

    /* Secondary Colors - Sophisticated Amber */
    --secondary-color: #FFB74D;
    --secondary-light: #FFCC80;
    --secondary-dark: #FF9800;

    /* Accent Colors - Modern Cyan */
    --accent-color: #26C6DA;
    --accent-light: #4DD0E1;
    --accent-dark: #00ACC1;

    /* Neutral Colors - Premium Dark Theme */
    --background-color: #0D1117;
    --surface-color: #161B22;
    --card-background: #21262D;
    --glass-background: rgba(255, 255, 255, 0.08);
    --glass-border: rgba(255, 255, 255, 0.1);
    --text-primary: #F0F6FC;
    --text-secondary: #C9D1D9;
    --text-muted: #8B949E;
    --border-color: #30363D;
    --divider-color: #21262D;
    
    /* Status Colors */
    --success-color: #4caf50;
    --warning-color: #ff9800;
    --error-color: #f44336;
    --info-color: #2196f3;
    
    /* Gradients - Modern 2025 */
    --gradient-primary: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    --gradient-accent: linear-gradient(135deg, var(--accent-color), var(--primary-color));
    --gradient-dark: linear-gradient(135deg, var(--background-color), var(--surface-color));
    --gradient-card: linear-gradient(135deg, rgba(255, 255, 255, 0.12), rgba(255, 255, 255, 0.06));
    --gradient-glass: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));

    /* Glassmorphism Effects */
    --glass-blur: blur(20px);
    --glass-blur-light: blur(10px);
    --glass-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    --glass-shadow-light: 0 4px 16px rgba(0, 0, 0, 0.2);
    
    /* Spacing - Enhanced 2025 Scale */
    --spacing-xs: 0.25rem;    /* 4px */
    --spacing-sm: 0.5rem;     /* 8px */
    --spacing-md: 1rem;       /* 16px */
    --spacing-lg: 1.5rem;     /* 24px */
    --spacing-xl: 2rem;       /* 32px */
    --spacing-xxl: 3rem;      /* 48px */

    /* Layout Gaps - Consistent spacing for modern layouts */
    --gap-card: var(--spacing-lg);        /* 24px - Standard card spacing */
    --gap-section: var(--spacing-xl);     /* 32px - Section spacing */
    --gap-component: var(--spacing-md);   /* 16px - Component spacing */
    --gap-mobile: var(--spacing-sm);      /* 8px - Mobile spacing */

    /* Z-Index Scale - Proper layering system */
    --z-base: 1;
    --z-dropdown: 100;
    --z-sticky: 200;
    --z-fixed: 300;
    --z-modal-backdrop: 400;
    --z-modal: 500;
    --z-popover: 600;
    --z-tooltip: 700;
    --z-toast: 800;
    
    /* Typography - Modern 2025 Stack */
    --font-family-primary: 'Inter', 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    --font-family-heading: 'Poppins', 'Inter', 'SF Pro Display', -apple-system, BlinkMacSystemFont, sans-serif;
    --font-family-mono: 'JetBrains Mono', 'Fira Code', 'Monaco', 'Consolas', monospace;
    
    --font-size-xs: 0.75rem;
    --font-size-sm: 0.875rem;
    --font-size-base: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    --font-size-2xl: 1.5rem;
    --font-size-3xl: 1.875rem;
    --font-size-4xl: 2.25rem;
    
    --font-weight-light: 300;
    --font-weight-normal: 400;
    --font-weight-medium: 500;
    --font-weight-semibold: 600;
    --font-weight-bold: 700;
    
    --line-height-tight: 1.25;
    --line-height-normal: 1.5;
    --line-height-relaxed: 1.75;
    
    /* Border Radius */
    --border-radius-sm: 0.25rem;
    --border-radius-md: 0.5rem;
    --border-radius-lg: 0.75rem;
    --border-radius-xl: 1rem;
    --border-radius-full: 9999px;
    
    /* Shadows - Enhanced for Dark Theme */
    --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.3);
    --shadow-md: 0 4px 12px -2px rgba(0, 0, 0, 0.4), 0 2px 8px -2px rgba(0, 0, 0, 0.2);
    --shadow-lg: 0 12px 24px -4px rgba(0, 0, 0, 0.5), 0 4px 16px -4px rgba(0, 0, 0, 0.3);
    --shadow-xl: 0 24px 48px -8px rgba(0, 0, 0, 0.6), 0 8px 32px -8px rgba(0, 0, 0, 0.4);
    --shadow-glow: 0 0 20px rgba(255, 112, 67, 0.3);
    --shadow-glow-accent: 0 0 20px rgba(38, 198, 218, 0.3);
    
    /* Transitions */
    --transition-fast: 0.15s ease-in-out;
    --transition-normal: 0.3s ease-in-out;
    --transition-slow: 0.5s ease-in-out;
    
    /* Z-Index - Modern layering system */
    --z-base: 1;
    --z-dropdown: 100;
    --z-sticky: 200;
    --z-fixed: 300;
    --z-modal-backdrop: 400;
    --z-modal: 500;
    --z-popover: 600;
    --z-tooltip: 700;
    --z-overlay: 800;

    /* Layout - Flexible modern system */
    --container-max-width: 1400px;
    --sidebar-width: 280px;
    --sidebar-width-collapsed: 80px;
    --header-height: 64px;
    --mobile-nav-height: 60px;
    --content-max-width: 680px;

    /* Spacing System - Consistent gaps */
    --gap-xs: 0.5rem;
    --gap-sm: 0.75rem;
    --gap-md: 1rem;
    --gap-lg: 1.5rem;
    --gap-xl: 2rem;
    --gap-2xl: 3rem;
    --gap-card: 1.25rem;
    --gap-section: 2rem;
    --gap-component: 1rem;
    --gap-mobile: 1rem;
    
    /* Breakpoints (for reference in media queries) */
    --breakpoint-sm: 640px;
    --breakpoint-md: 768px;
    --breakpoint-lg: 1024px;
    --breakpoint-xl: 1280px;
    --breakpoint-2xl: 1536px;
}

/* Dark theme adjustments */
@media (prefers-color-scheme: dark) {
    :root {
        --background-color: #0a0a0a;
        --surface-color: #1a1a1a;
        --card-background: #2a2a2a;
        --border-color: #333333;
        --divider-color: #444444;
    }
}

/* Light theme override */
[data-theme="light"] {
    --background-color: #ffffff;
    --surface-color: #f8f9fa;
    --card-background: #ffffff;
    --text-primary: #1a1a1a;
    --text-secondary: #4a4a4a;
    --text-muted: #6a6a6a;
    --border-color: #e0e0e0;
    --divider-color: #f0f0f0;
}

/* High contrast theme */
[data-theme="high-contrast"] {
    --background-color: #000000;
    --surface-color: #000000;
    --card-background: #000000;
    --text-primary: #ffffff;
    --text-secondary: #ffffff;
    --text-muted: #cccccc;
    --border-color: #ffffff;
    --divider-color: #ffffff;
    --primary-color: #ffff00;
    --secondary-color: #00ffff;
}
