<?xml version="1.0" encoding="UTF-8"?>
<svg width="76" height="76" viewBox="0 0 76 76" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- Main gradient -->
    <linearGradient id="mainGrad76" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#f59e0b;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#f97316;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#d97706;stop-opacity:1" />
    </linearGradient>

    <!-- Highlight gradient -->
    <linearGradient id="highlight76" x1="0%" y1="0%" x2="100%" y2="50%">
      <stop offset="0%" style="stop-color:#ffffff;stop-opacity:0.3" />
      <stop offset="100%" style="stop-color:#ffffff;stop-opacity:0" />
    </linearGradient>

    <!-- Shadow filter -->
    <filter id="shadow76" x="-20%" y="-20%" width="140%" height="140%">
      <feDropShadow dx="0" dy="1.52" stdDeviation="0.76" flood-color="#000000" flood-opacity="0.3"/>
    </filter>
  </defs>

  <!-- Main background -->
  <rect width="76" height="76" rx="16.72" fill="url(#mainGrad76)" filter="url(#shadow76)"/>

  <!-- Highlight overlay -->
  <rect width="76" height="45.6" rx="16.72" fill="url(#highlight76)"/>

  <!-- Text shadow -->
  <text x="50%" y="51.52%" dominant-baseline="middle" text-anchor="middle"
        font-family="SF Pro Display, -apple-system, Arial, sans-serif" font-weight="700"
        font-size="34.2" fill="#000000" opacity="0.2">N</text>

  <!-- Main text -->
  <text x="50%" y="50%" dominant-baseline="middle" text-anchor="middle"
        font-family="SF Pro Display, -apple-system, Arial, sans-serif" font-weight="700"
        font-size="34.2" fill="white">N</text>
</svg>