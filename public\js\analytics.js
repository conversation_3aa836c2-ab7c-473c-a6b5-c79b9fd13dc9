// Lightweight Analytics Client with local queue and best-effort send

class AnalyticsClient {
    constructor() {
        this.endpoint = '/api/metrics/performance';
        this.queueKey = 'naroop.analytics.queue.v1';
        this.sessionKey = 'naroop.analytics.session.v1';
        this.maxQueue = 200;
        this.flushInProgress = false;
        this.queue = this.loadQueue();
        this.sessionId = this.loadOrCreateSession();
        this.flushOnUnload();
    }

    loadQueue() {
        try {
            const raw = localStorage.getItem(this.queueKey);
            return raw ? JSON.parse(raw) : [];
        } catch {
            return [];
        }
    }

    saveQueue() {
        try {
            localStorage.setItem(this.queueKey, JSON.stringify(this.queue.slice(-this.maxQueue)));
        } catch {}
    }

    loadOrCreateSession() {
        try {
            const existing = localStorage.getItem(this.sessionKey);
            if (existing) return existing;
            const id = `${Date.now().toString(36)}-${Math.random().toString(36).slice(2, 10)}`;
            localStorage.setItem(this.sessionKey, id);
            return id;
        } catch {
            return `nos-${Math.random().toString(36).slice(2, 10)}`;
        }
    }

    track(event, props = {}) {
        const payload = {
            event,
            props,
            ts: Date.now(),
            url: location.href,
            ref: document.referrer || null,
            ua: navigator.userAgent,
            sessionId: this.sessionId
        };
        this.queue.push(payload);
        this.saveQueue();
        // Best effort, non-blocking
        this.flushSoon();
    }

    flushSoon() {
        if (this.flushInProgress) return;
        this.flushInProgress = true;
        setTimeout(() => this.flush().finally(() => (this.flushInProgress = false)), 0);
    }

    async flush() {
        if (!this.queue.length) return;
        const batch = this.queue.slice(0, 20);
        const body = JSON.stringify({ events: batch, userAgent: navigator.userAgent, url: location.href, timestamp: new Date().toISOString() });

        // Try sendBeacon first
        try {
            if (navigator.sendBeacon) {
                const blob = new Blob([body], { type: 'application/json' });
                const ok = navigator.sendBeacon(this.endpoint, blob);
                if (ok) {
                    this.queue = this.queue.slice(batch.length);
                    this.saveQueue();
                    return;
                }
            }
        } catch {}

        // Fallback to fetch (may fail on static server)
        try {
            const res = await fetch(this.endpoint, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body,
                keepalive: true
            });
            if (res && res.ok) {
                this.queue = this.queue.slice(batch.length);
                this.saveQueue();
            }
        } catch {
            // Keep events in queue if failed
        }
    }

    flushOnUnload() {
        const handler = () => {
            if (!this.queue.length) return;
            try {
                const body = JSON.stringify({ events: this.queue.splice(0, this.queue.length), url: location.href, timestamp: new Date().toISOString() });
                if (navigator.sendBeacon) {
                    const blob = new Blob([body], { type: 'application/json' });
                    navigator.sendBeacon(this.endpoint, blob);
                }
            } catch {}
        };
        window.addEventListener('visibilitychange', () => {
            if (document.visibilityState === 'hidden') handler();
        });
        window.addEventListener('pagehide', handler);
        window.addEventListener('beforeunload', handler);
    }
}

const analytics = new AnalyticsClient();
export default analytics;
export { AnalyticsClient, analytics };
