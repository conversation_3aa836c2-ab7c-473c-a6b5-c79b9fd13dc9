// Firebase Configuration and Authentication Module
// This module handles Firebase initialization and authentication

class FirebaseAuth {
    constructor() {
        this.auth = null;
        this.db = null;
        this.initialized = false;
        this.config = null;
    }

    // Initialize Firebase
    async initialize() {
        try {
            // Fetch Firebase configuration from server
            const response = await fetch(`/api/firebase-config`);
            this.config = await response.json();

            if (this.config.isDemoMode) {
                console.warn('🔧 Firebase running in demo mode');
                this.initializeDemoMode();
                return;
            }

            // Import Firebase modules dynamically
            const { initializeApp } = await import('https://www.gstatic.com/firebasejs/10.7.1/firebase-app.js');
            const { getAuth, connectAuthEmulator } = await import('https://www.gstatic.com/firebasejs/10.7.1/firebase-auth.js');
            const { getFirestore, connectFirestoreEmulator } = await import('https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js');

            // Initialize Firebase app
            const app = initializeApp(this.config);
            this.auth = getAuth(app);
            this.db = getFirestore(app);

            // Connect to emulators only if explicitly enabled
            const useEmu = (this.config && this.config.useEmulator === true) || localStorage.getItem('useFirebaseEmulator') === 'true';
            if (useEmu) {
                try {
                    connectAuthEmulator(this.auth, 'http://localhost:9099');
                    connectFirestoreEmulator(this.db, 'localhost', 8080);
                    console.warn('⚠️ Using Firebase Emulators (auth/firestore)');
                } catch (error) {
                    console.log('Emulators not available, using production Firebase');
                }
            }

            this.initialized = true;
            console.log('✅ Firebase initialized successfully');
            
            // Set up auth state listener
            this.setupAuthStateListener();
            
        } catch (error) {
            console.error('❌ Firebase initialization failed:', error);
            this.initializeDemoMode();
        }
    }

    // Initialize demo mode for development
    initializeDemoMode() {
        console.log('🔧 Initializing Firebase demo mode');
        this.initialized = true;
        
        // Create mock auth object
        this.auth = {
            currentUser: null,
            onAuthStateChanged: (callback) => {
                // Simulate no user logged in
                setTimeout(() => callback(null), 100);
                return () => {}; // Unsubscribe function
            }
        };
        
        this.setupAuthStateListener();
    }

    // Set up authentication state listener
    setupAuthStateListener() {
        if (!this.auth) return;

        this.auth.onAuthStateChanged((user) => {
            if (user) {
                console.log('👤 User signed in:', user.email);
                this.handleUserSignedIn(user);
            } else {
                console.log('👤 User signed out');
                this.handleUserSignedOut();
            }
        });
    }

    // Handle user signed in
    async handleUserSignedIn(user) {
        try {
            // Get ID token for server authentication
            const idToken = await user.getIdToken();
            
            // Store user info in localStorage
            localStorage.setItem('naroop_user', JSON.stringify({
                uid: user.uid,
                email: user.email,
                displayName: user.displayName,
                emailVerified: user.emailVerified
            }));

            // Create or update user profile on server
            await this.syncUserProfile(user, idToken);
            
            // Redirect to main app if on landing page
            if (window.location.pathname === '/' || window.location.pathname === '/landing.html') {
                window.location.href = '/app';
            }
            
        } catch (error) {
            console.error('Error handling user sign in:', error);
        }
    }

    // Handle user signed out
    handleUserSignedOut() {
        // Clear user info from localStorage
        localStorage.removeItem('naroop_user');
        
        // Redirect to landing page if on protected page
        if (window.location.pathname === '/app' || window.location.pathname === '/index.html') {
            window.location.href = '/';
        }
    }

    // Sync user profile with server
    async syncUserProfile(user, idToken) {
        try {
            const response = await fetch('/api/login', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${idToken}`
                },
                body: JSON.stringify({
                    uid: user.uid,
                    email: user.email
                })
            });

            if (!response.ok) {
                throw new Error('Failed to sync user profile');
            }

            const result = await response.json();
            console.log('✅ User profile synced:', result);
            
        } catch (error) {
            console.error('Error syncing user profile:', error);
        }
    }

    // Sign in with email and password
    async signInWithEmailAndPassword(email, password) {
        if (!this.initialized || this.config?.isDemoMode) {
            // Demo mode - simulate sign in
            console.log('🔧 Demo sign in:', email);
            return { user: { uid: 'demo-user', email: email } };
        }

        try {
            const { signInWithEmailAndPassword } = await import('https://www.gstatic.com/firebasejs/10.7.1/firebase-auth.js');
            const userCredential = await signInWithEmailAndPassword(this.auth, email, password);
            return userCredential;
        } catch (error) {
            console.error('Sign in error:', error);
            throw error;
        }
    }

    // Create user with email and password
    async createUserWithEmailAndPassword(email, password) {
        if (!this.initialized || this.config?.isDemoMode) {
            // Demo mode - simulate sign up
            console.log('🔧 Demo sign up:', email);
            return { user: { uid: 'demo-user-' + Date.now(), email: email } };
        }

        try {
            const { createUserWithEmailAndPassword } = await import('https://www.gstatic.com/firebasejs/10.7.1/firebase-auth.js');
            const userCredential = await createUserWithEmailAndPassword(this.auth, email, password);
            return userCredential;
        } catch (error) {
            console.error('Sign up error:', error);
            throw error;
        }
    }

    // Sign out
    async signOut() {
        if (!this.initialized || this.config?.isDemoMode) {
            // Demo mode - simulate sign out
            console.log('🔧 Demo sign out');
            this.handleUserSignedOut();
            return;
        }

        try {
            const { signOut } = await import('https://www.gstatic.com/firebasejs/10.7.1/firebase-auth.js');
            await signOut(this.auth);
        } catch (error) {
            console.error('Sign out error:', error);
            throw error;
        }
    }

    // Get current user
    getCurrentUser() {
        if (this.config?.isDemoMode) {
            const storedUser = localStorage.getItem('naroop_user');
            return storedUser ? JSON.parse(storedUser) : null;
        }
        
        return this.auth?.currentUser || null;
    }

    // Check if user is authenticated
    isAuthenticated() {
        return this.getCurrentUser() !== null;
    }
}

// Create and export Firebase Auth instance
const firebaseAuth = new FirebaseAuth();

// Auto-initialize when module loads
firebaseAuth.initialize();

export { FirebaseAuth, firebaseAuth };
export default firebaseAuth;
