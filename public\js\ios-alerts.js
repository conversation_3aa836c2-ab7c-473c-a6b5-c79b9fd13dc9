/**
 * iOS-Style Alerts System
 * Replaces browser alerts with native iOS-style modal alerts
 */

class iOSAlerts {
    constructor() {
        this.isIOS = this.detectIOS();
        this.activeAlerts = new Set();
        this.alertQueue = [];
        
        this.init();
    }

    detectIOS() {
        return /iPad|iPhone|iPod/.test(navigator.userAgent) || 
               (navigator.platform === 'MacIntel' && navigator.maxTouchPoints > 1) ||
               window.Capacitor?.getPlatform() === 'ios';
    }

    init() {
        console.log('🚨 Initializing iOS-style alerts...');

        this.createAlertContainer();
        this.overrideBrowserAlerts();
        this.setupKeyboardHandling();
        
        console.log('✅ iOS-style alerts initialized');
    }

    createAlertContainer() {
        // Create container for alerts
        this.alertContainer = document.createElement('div');
        this.alertContainer.className = 'ios-alert-container';
        document.body.appendChild(this.alertContainer);
    }

    overrideBrowserAlerts() {
        // Store original functions
        this.originalAlert = window.alert;
        this.originalConfirm = window.confirm;
        this.originalPrompt = window.prompt;

        // Override with iOS-style versions
        window.alert = (message) => this.showAlert(message);
        window.confirm = (message) => this.showConfirm(message);
        window.prompt = (message, defaultValue) => this.showPrompt(message, defaultValue);
    }

    setupKeyboardHandling() {
        // Handle escape key to close alerts
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && this.activeAlerts.size > 0) {
                this.dismissTopAlert();
            }
        });
    }

    async showAlert(message, title = 'Naroop', options = {}) {
        return new Promise((resolve) => {
            const alertConfig = {
                type: 'alert',
                title,
                message,
                buttons: [
                    { text: 'OK', style: 'default', action: () => resolve(true) }
                ],
                ...options
            };

            this.createAlert(alertConfig);
        });
    }

    async showConfirm(message, title = 'Confirm', options = {}) {
        return new Promise((resolve) => {
            const alertConfig = {
                type: 'confirm',
                title,
                message,
                buttons: [
                    { text: 'Cancel', style: 'cancel', action: () => resolve(false) },
                    { text: 'OK', style: 'default', action: () => resolve(true) }
                ],
                ...options
            };

            this.createAlert(alertConfig);
        });
    }

    async showPrompt(message, defaultValue = '', title = 'Input', options = {}) {
        return new Promise((resolve) => {
            const alertConfig = {
                type: 'prompt',
                title,
                message,
                defaultValue,
                buttons: [
                    { text: 'Cancel', style: 'cancel', action: () => resolve(null) },
                    { text: 'OK', style: 'default', action: (value) => resolve(value) }
                ],
                ...options
            };

            this.createAlert(alertConfig);
        });
    }

    async showActionSheet(title, message, actions, options = {}) {
        return new Promise((resolve) => {
            const alertConfig = {
                type: 'actionSheet',
                title,
                message,
                buttons: [
                    ...actions,
                    { text: 'Cancel', style: 'cancel', action: () => resolve(null) }
                ],
                ...options
            };

            this.createAlert(alertConfig);
        });
    }

    createAlert(config) {
        const alertId = 'alert-' + Date.now() + '-' + Math.random().toString(36).substr(2, 9);
        
        // Create alert overlay
        const overlay = document.createElement('div');
        overlay.className = 'ios-alert-overlay';
        overlay.id = alertId;
        
        // Create alert content
        const alert = document.createElement('div');
        alert.className = `ios-alert ios-alert-${config.type}`;
        
        // Alert header
        if (config.title) {
            const title = document.createElement('div');
            title.className = 'ios-alert-title';
            title.textContent = config.title;
            alert.appendChild(title);
        }
        
        // Alert message
        if (config.message) {
            const message = document.createElement('div');
            message.className = 'ios-alert-message';
            message.textContent = config.message;
            alert.appendChild(message);
        }
        
        // Input field for prompt
        let inputField = null;
        if (config.type === 'prompt') {
            inputField = document.createElement('input');
            inputField.type = 'text';
            inputField.className = 'ios-alert-input';
            inputField.value = config.defaultValue || '';
            inputField.placeholder = config.placeholder || '';
            alert.appendChild(inputField);
        }
        
        // Alert buttons
        const buttonsContainer = document.createElement('div');
        buttonsContainer.className = 'ios-alert-buttons';
        
        config.buttons.forEach((button, index) => {
            const btn = document.createElement('button');
            btn.className = `ios-alert-button ios-alert-button-${button.style || 'default'}`;
            btn.textContent = button.text;
            
            btn.addEventListener('click', () => {
                // Trigger haptic feedback
                if (window.iOSHaptics) {
                    if (button.style === 'destructive') {
                        window.iOSHaptics.warning();
                    } else if (button.style === 'cancel') {
                        window.iOSHaptics.light();
                    } else {
                        window.iOSHaptics.medium();
                    }
                }
                
                // Get input value for prompt
                const value = inputField ? inputField.value : undefined;
                
                // Execute button action
                if (button.action) {
                    button.action(value);
                }
                
                // Close alert
                this.dismissAlert(alertId);
            });
            
            buttonsContainer.appendChild(btn);
            
            // Focus first non-cancel button
            if (index === 0 && button.style !== 'cancel') {
                setTimeout(() => btn.focus(), 100);
            }
        });
        
        alert.appendChild(buttonsContainer);
        overlay.appendChild(alert);
        
        // Add to container
        this.alertContainer.appendChild(overlay);
        this.activeAlerts.add(alertId);
        
        // Animate in
        requestAnimationFrame(() => {
            overlay.classList.add('show');
            
            // Focus input field for prompts
            if (inputField) {
                setTimeout(() => {
                    inputField.focus();
                    inputField.select();
                }, 300);
            }
        });
        
        // Handle overlay click to dismiss (for action sheets)
        if (config.type === 'actionSheet') {
            overlay.addEventListener('click', (e) => {
                if (e.target === overlay) {
                    this.dismissAlert(alertId);
                    if (config.buttons.find(b => b.style === 'cancel')?.action) {
                        config.buttons.find(b => b.style === 'cancel').action();
                    }
                }
            });
        }
        
        return alertId;
    }

    dismissAlert(alertId) {
        const overlay = document.getElementById(alertId);
        if (!overlay) return;
        
        overlay.classList.remove('show');
        this.activeAlerts.delete(alertId);
        
        setTimeout(() => {
            if (overlay.parentNode) {
                overlay.parentNode.removeChild(overlay);
            }
        }, 300);
    }

    dismissTopAlert() {
        if (this.activeAlerts.size > 0) {
            const topAlertId = Array.from(this.activeAlerts).pop();
            this.dismissAlert(topAlertId);
        }
    }

    dismissAllAlerts() {
        this.activeAlerts.forEach(alertId => {
            this.dismissAlert(alertId);
        });
    }

    // Convenience methods for common alert types
    async showSuccess(message, title = 'Success') {
        return this.showAlert(message, title, {
            buttons: [{ text: 'OK', style: 'default' }]
        });
    }

    async showError(message, title = 'Error') {
        return this.showAlert(message, title, {
            buttons: [{ text: 'OK', style: 'destructive' }]
        });
    }

    async showWarning(message, title = 'Warning') {
        return this.showAlert(message, title, {
            buttons: [
                { text: 'Cancel', style: 'cancel' },
                { text: 'Continue', style: 'destructive' }
            ]
        });
    }

    async showDeleteConfirm(itemName = 'this item') {
        return this.showConfirm(
            `Are you sure you want to delete ${itemName}? This action cannot be undone.`,
            'Delete Confirmation',
            {
                buttons: [
                    { text: 'Cancel', style: 'cancel', action: () => false },
                    { text: 'Delete', style: 'destructive', action: () => true }
                ]
            }
        );
    }

    async showLogoutConfirm() {
        return this.showConfirm(
            'Are you sure you want to log out?',
            'Log Out',
            {
                buttons: [
                    { text: 'Cancel', style: 'cancel', action: () => false },
                    { text: 'Log Out', style: 'destructive', action: () => true }
                ]
            }
        );
    }

    // Restore original browser alerts (for debugging)
    restoreOriginalAlerts() {
        window.alert = this.originalAlert;
        window.confirm = this.originalConfirm;
        window.prompt = this.originalPrompt;
    }
}

// Auto-initialize when DOM is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        window.iOSAlerts = new iOSAlerts();
    });
} else {
    window.iOSAlerts = new iOSAlerts();
}

// Export for module use
if (typeof module !== 'undefined' && module.exports) {
    module.exports = iOSAlerts;
}
