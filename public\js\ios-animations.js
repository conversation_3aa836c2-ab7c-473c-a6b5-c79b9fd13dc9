/**
 * iOS Animation Optimization System
 * Ensures smooth 60fps animations with native iOS feel
 */

class iOSAnimations {
    constructor() {
        this.isIOS = this.detectIOS();
        this.animationQueue = [];
        this.isAnimating = false;
        this.preferredFrameRate = 60;
        this.animationSettings = {
            duration: 300,
            easing: 'cubic-bezier(0.25, 0.46, 0.45, 0.94)', // iOS default
            reducedMotion: false
        };
        
        this.init();
    }

    detectIOS() {
        return /iPad|iPhone|iPod/.test(navigator.userAgent) || 
               (navigator.platform === 'MacIntel' && navigator.maxTouchPoints > 1) ||
               window.Capacitor?.getPlatform() === 'ios';
    }

    init() {
        console.log('✨ Initializing iOS animation optimizations...');

        this.detectReducedMotion();
        this.optimizeExistingAnimations();
        this.setupPerformanceMonitoring();
        this.addAnimationHelpers();
        
        console.log('✅ iOS animation optimizations initialized');
    }

    detectReducedMotion() {
        // Respect user's reduced motion preference
        const prefersReducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)');
        this.animationSettings.reducedMotion = prefersReducedMotion.matches;
        
        prefersReducedMotion.addEventListener('change', (e) => {
            this.animationSettings.reducedMotion = e.matches;
            this.updateAnimationDurations();
        });
    }

    optimizeExistingAnimations() {
        // Add will-change property to animated elements
        const animatedElements = document.querySelectorAll(`
            .btn, .action-btn, .post-card, .story-card, .modal,
            .ios-alert, .ios-toast, .ios-refresh-indicator,
            .page-content, .main-content
        `);

        animatedElements.forEach(element => {
            this.optimizeElement(element);
        });

        // Monitor for new animated elements
        const observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                mutation.addedNodes.forEach((node) => {
                    if (node.nodeType === 1) {
                        if (this.shouldOptimize(node)) {
                            this.optimizeElement(node);
                        }
                        // Check child elements
                        const childElements = node.querySelectorAll(`
                            .btn, .action-btn, .post-card, .story-card, .modal,
                            .ios-alert, .ios-toast, .ios-refresh-indicator
                        `);
                        childElements.forEach(child => this.optimizeElement(child));
                    }
                });
            });
        });

        observer.observe(document.body, { childList: true, subtree: true });
    }

    optimizeElement(element) {
        // Add hardware acceleration
        element.style.transform = element.style.transform || 'translateZ(0)';
        element.style.backfaceVisibility = 'hidden';
        element.style.perspective = '1000px';
        
        // Optimize for animations
        if (this.shouldOptimize(element)) {
            element.style.willChange = 'transform, opacity';
        }
    }

    shouldOptimize(element) {
        const optimizeClasses = [
            'btn', 'action-btn', 'post-card', 'story-card', 'modal',
            'ios-alert', 'ios-toast', 'ios-refresh-indicator'
        ];
        
        return optimizeClasses.some(className => 
            element.classList.contains(className)
        );
    }

    setupPerformanceMonitoring() {
        // Monitor frame rate
        let lastTime = performance.now();
        let frameCount = 0;
        let fps = 60;

        const measureFPS = (currentTime) => {
            frameCount++;
            
            if (currentTime - lastTime >= 1000) {
                fps = Math.round((frameCount * 1000) / (currentTime - lastTime));
                frameCount = 0;
                lastTime = currentTime;
                
                // Adjust animation quality based on performance
                this.adjustAnimationQuality(fps);
            }
            
            requestAnimationFrame(measureFPS);
        };

        requestAnimationFrame(measureFPS);
    }

    adjustAnimationQuality(fps) {
        if (fps < 45) {
            // Reduce animation complexity for better performance
            document.body.classList.add('reduced-animations');
            this.animationSettings.duration = 200;
        } else if (fps > 55) {
            // Restore full animation quality
            document.body.classList.remove('reduced-animations');
            this.animationSettings.duration = 300;
        }
    }

    updateAnimationDurations() {
        const duration = this.animationSettings.reducedMotion ? 0 : this.animationSettings.duration;
        
        document.documentElement.style.setProperty('--animation-duration', `${duration}ms`);
        document.documentElement.style.setProperty('--animation-easing', this.animationSettings.easing);
    }

    addAnimationHelpers() {
        // Add CSS custom properties for consistent animations
        this.updateAnimationDurations();
        
        // Add utility classes
        const style = document.createElement('style');
        style.textContent = `
            .ios-animate {
                transition: all var(--animation-duration, 300ms) var(--animation-easing, cubic-bezier(0.25, 0.46, 0.45, 0.94));
            }
            
            .ios-animate-transform {
                transition: transform var(--animation-duration, 300ms) var(--animation-easing, cubic-bezier(0.25, 0.46, 0.45, 0.94));
            }
            
            .ios-animate-opacity {
                transition: opacity var(--animation-duration, 300ms) var(--animation-easing, cubic-bezier(0.25, 0.46, 0.45, 0.94));
            }
            
            .reduced-animations * {
                animation-duration: 0.1s !important;
                transition-duration: 0.1s !important;
            }
            
            @media (prefers-reduced-motion: reduce) {
                * {
                    animation-duration: 0.01ms !important;
                    animation-iteration-count: 1 !important;
                    transition-duration: 0.01ms !important;
                }
            }
        `;
        document.head.appendChild(style);
    }

    // Animation utilities
    fadeIn(element, duration = this.animationSettings.duration) {
        return this.animate(element, {
            opacity: [0, 1]
        }, duration);
    }

    fadeOut(element, duration = this.animationSettings.duration) {
        return this.animate(element, {
            opacity: [1, 0]
        }, duration);
    }

    slideInFromRight(element, duration = this.animationSettings.duration) {
        return this.animate(element, {
            transform: ['translateX(100%)', 'translateX(0)'],
            opacity: [0, 1]
        }, duration);
    }

    slideInFromLeft(element, duration = this.animationSettings.duration) {
        return this.animate(element, {
            transform: ['translateX(-100%)', 'translateX(0)'],
            opacity: [0, 1]
        }, duration);
    }

    slideUp(element, duration = this.animationSettings.duration) {
        return this.animate(element, {
            transform: ['translateY(100%)', 'translateY(0)'],
            opacity: [0, 1]
        }, duration);
    }

    scaleIn(element, duration = this.animationSettings.duration) {
        return this.animate(element, {
            transform: ['scale(0.8)', 'scale(1)'],
            opacity: [0, 1]
        }, duration);
    }

    bounce(element, intensity = 0.1) {
        return this.animate(element, {
            transform: [
                'scale(1)',
                `scale(${1 + intensity})`,
                'scale(1)'
            ]
        }, 200);
    }

    shake(element, intensity = 5) {
        return this.animate(element, {
            transform: [
                'translateX(0)',
                `translateX(-${intensity}px)`,
                `translateX(${intensity}px)`,
                `translateX(-${intensity}px)`,
                `translateX(${intensity}px)`,
                'translateX(0)'
            ]
        }, 300);
    }

    pulse(element, scale = 1.05) {
        return this.animate(element, {
            transform: [
                'scale(1)',
                `scale(${scale})`,
                'scale(1)'
            ]
        }, 600);
    }

    animate(element, keyframes, duration = this.animationSettings.duration) {
        if (this.animationSettings.reducedMotion) {
            duration = 0;
        }

        const animation = element.animate(keyframes, {
            duration,
            easing: this.animationSettings.easing,
            fill: 'forwards'
        });

        // Clean up will-change after animation
        animation.addEventListener('finish', () => {
            element.style.willChange = 'auto';
        });

        return animation;
    }

    // Batch animations for better performance
    batchAnimate(animations) {
        return Promise.all(animations.map(({ element, keyframes, duration }) => 
            this.animate(element, keyframes, duration)
        ));
    }

    // Stagger animations
    staggerAnimate(elements, keyframes, duration = this.animationSettings.duration, staggerDelay = 50) {
        const animations = [];
        
        elements.forEach((element, index) => {
            setTimeout(() => {
                animations.push(this.animate(element, keyframes, duration));
            }, index * staggerDelay);
        });
        
        return Promise.all(animations);
    }

    // Performance-optimized scroll animations
    onScroll(callback, throttle = 16) {
        let ticking = false;
        
        const handleScroll = () => {
            if (!ticking) {
                requestAnimationFrame(() => {
                    callback();
                    ticking = false;
                });
                ticking = true;
            }
        };
        
        window.addEventListener('scroll', handleScroll, { passive: true });
        
        return () => window.removeEventListener('scroll', handleScroll);
    }

    // Intersection Observer for scroll-triggered animations
    observeIntersection(elements, callback, options = {}) {
        const defaultOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px',
            ...options
        };
        
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    callback(entry.target, entry);
                }
            });
        }, defaultOptions);
        
        elements.forEach(element => observer.observe(element));
        
        return observer;
    }
}

// Auto-initialize when DOM is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        window.iOSAnimations = new iOSAnimations();
    });
} else {
    window.iOSAnimations = new iOSAnimations();
}

// Export for module use
if (typeof module !== 'undefined' && module.exports) {
    module.exports = iOSAnimations;
}
