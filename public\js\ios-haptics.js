/**
 * iOS Haptic Feedback System
 * Provides native-feeling haptic feedback for iOS devices
 */

class iOSHaptics {
    constructor() {
        this.isIOS = this.detectIOS();
        this.isCapacitor = window.Capacitor !== undefined;
        this.hapticPlugin = null;
        
        this.init();
    }

    detectIOS() {
        return /iPad|iPhone|iPod/.test(navigator.userAgent) || 
               (navigator.platform === 'MacIntel' && navigator.maxTouchPoints > 1) ||
               window.Capacitor?.getPlatform() === 'ios';
    }

    async init() {
        if (!this.isIOS) {
            console.log('📱 Haptics: Not iOS device, using fallback vibration');
            return;
        }

        console.log('🍎 Initializing iOS haptic feedback...');

        // Try to use Capacitor Haptics plugin if available
        if (this.isCapacitor && window.Capacitor.Plugins.Haptics) {
            this.hapticPlugin = window.Capacitor.Plugins.Haptics;
            console.log('✅ Using Capacitor Haptics plugin');
        } else {
            console.log('📱 Using web vibration API fallback');
        }

        this.setupHapticEvents();
        console.log('✅ iOS haptic feedback initialized');
    }

    setupHapticEvents() {
        // Light haptic for button taps
        this.addHapticToElements([
            'button:not(.no-haptic)',
            '.btn:not(.no-haptic)',
            '.action-btn:not(.no-haptic)',
            '.clickable:not(.no-haptic)'
        ], 'light');

        // Medium haptic for important actions
        this.addHapticToElements([
            '.create-post-btn',
            '.submit-btn',
            '.primary-btn',
            '.login-btn',
            '.signup-btn'
        ], 'medium');

        // Heavy haptic for critical actions
        this.addHapticToElements([
            '.delete-btn',
            '.logout-btn',
            '.confirm-btn'
        ], 'heavy');

        // Selection haptic for toggles and switches
        this.addHapticToElements([
            'input[type="checkbox"]',
            'input[type="radio"]',
            '.toggle',
            '.switch'
        ], 'selection');

        // Custom haptics for specific interactions
        this.setupCustomHaptics();
    }

    addHapticToElements(selectors, intensity = 'light') {
        selectors.forEach(selector => {
            const elements = document.querySelectorAll(selector);
            elements.forEach(element => {
                element.addEventListener('touchstart', () => {
                    this.triggerHaptic(intensity);
                }, { passive: true });
            });
        });

        // Handle dynamically added elements
        const observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                mutation.addedNodes.forEach((node) => {
                    if (node.nodeType === 1) {
                        selectors.forEach(selector => {
                            if (node.matches && node.matches(selector)) {
                                node.addEventListener('touchstart', () => {
                                    this.triggerHaptic(intensity);
                                }, { passive: true });
                            }
                            // Also check child elements
                            const childElements = node.querySelectorAll(selector);
                            childElements.forEach(child => {
                                child.addEventListener('touchstart', () => {
                                    this.triggerHaptic(intensity);
                                }, { passive: true });
                            });
                        });
                    }
                });
            });
        });

        observer.observe(document.body, { childList: true, subtree: true });
    }

    setupCustomHaptics() {
        // Pull-to-refresh haptic
        document.addEventListener('pullToRefreshStart', () => {
            this.triggerHaptic('light');
        });

        document.addEventListener('pullToRefreshTriggered', () => {
            this.triggerHaptic('medium');
        });

        // Form validation haptics
        document.addEventListener('formValidationError', () => {
            this.triggerHaptic('error');
        });

        document.addEventListener('formSubmissionSuccess', () => {
            this.triggerHaptic('success');
        });

        // Navigation haptics
        document.addEventListener('pageTransition', () => {
            this.triggerHaptic('light');
        });

        // Post interaction haptics
        document.addEventListener('postLiked', () => {
            this.triggerHaptic('light');
        });

        document.addEventListener('postShared', () => {
            this.triggerHaptic('medium');
        });

        // Story interaction haptics
        document.addEventListener('storyViewed', () => {
            this.triggerHaptic('light');
        });
    }

    async triggerHaptic(intensity = 'light') {
        if (!this.isIOS) {
            this.fallbackVibration(intensity);
            return;
        }

        try {
            if (this.hapticPlugin) {
                // Use Capacitor Haptics plugin
                switch (intensity) {
                    case 'light':
                        await this.hapticPlugin.impact({ style: 'LIGHT' });
                        break;
                    case 'medium':
                        await this.hapticPlugin.impact({ style: 'MEDIUM' });
                        break;
                    case 'heavy':
                        await this.hapticPlugin.impact({ style: 'HEAVY' });
                        break;
                    case 'selection':
                        await this.hapticPlugin.selection();
                        break;
                    case 'success':
                        await this.hapticPlugin.notification({ type: 'SUCCESS' });
                        break;
                    case 'warning':
                        await this.hapticPlugin.notification({ type: 'WARNING' });
                        break;
                    case 'error':
                        await this.hapticPlugin.notification({ type: 'ERROR' });
                        break;
                    default:
                        await this.hapticPlugin.impact({ style: 'LIGHT' });
                }
            } else {
                // Fallback to web vibration API
                this.fallbackVibration(intensity);
            }
        } catch (error) {
            console.log('Haptic feedback failed:', error);
            this.fallbackVibration(intensity);
        }
    }

    fallbackVibration(intensity) {
        if (!navigator.vibrate) return;

        const patterns = {
            light: 10,
            medium: [10, 50, 10],
            heavy: [20, 100, 20],
            selection: 5,
            success: [10, 50, 10, 50, 10],
            warning: [50, 100, 50],
            error: [100, 50, 100, 50, 100]
        };

        const pattern = patterns[intensity] || patterns.light;
        navigator.vibrate(pattern);
    }

    // Public methods for manual haptic triggers
    light() {
        this.triggerHaptic('light');
    }

    medium() {
        this.triggerHaptic('medium');
    }

    heavy() {
        this.triggerHaptic('heavy');
    }

    selection() {
        this.triggerHaptic('selection');
    }

    success() {
        this.triggerHaptic('success');
    }

    warning() {
        this.triggerHaptic('warning');
    }

    error() {
        this.triggerHaptic('error');
    }

    // Disable haptics temporarily
    disable() {
        this.disabled = true;
    }

    // Re-enable haptics
    enable() {
        this.disabled = false;
    }
}

// Auto-initialize when DOM is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        window.iOSHaptics = new iOSHaptics();
    });
} else {
    window.iOSHaptics = new iOSHaptics();
}

// Export for module use
if (typeof module !== 'undefined' && module.exports) {
    module.exports = iOSHaptics;
}
