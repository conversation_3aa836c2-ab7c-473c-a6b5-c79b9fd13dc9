/**
 * iOS Keyboard Handling System
 * Manages iOS-specific keyboard behaviors and viewport adjustments
 */

class iOSKeyboard {
    constructor() {
        this.isIOS = this.detectIOS();
        this.isCapacitor = window.Capacitor !== undefined;
        this.keyboardPlugin = null;
        this.originalViewport = null;
        this.activeInput = null;
        this.keyboardHeight = 0;
        
        this.init();
    }

    detectIOS() {
        return /iPad|iPhone|iPod/.test(navigator.userAgent) || 
               (navigator.platform === 'MacIntel' && navigator.maxTouchPoints > 1) ||
               window.Capacitor?.getPlatform() === 'ios';
    }

    init() {
        if (!this.isIOS) return;

        console.log('⌨️ Initializing iOS keyboard handling...');

        // Store original viewport
        this.originalViewport = document.querySelector('meta[name=viewport]');
        
        // Try to use Capacitor Keyboard plugin if available
        if (this.isCapacitor && window.Capacitor.Plugins.Keyboard) {
            this.keyboardPlugin = window.Capacitor.Plugins.Keyboard;
            this.setupCapacitorKeyboard();
        }

        this.setupKeyboardEvents();
        this.setupInputHandling();
        this.setupViewportHandling();
        
        console.log('✅ iOS keyboard handling initialized');
    }

    setupCapacitorKeyboard() {
        // Listen for Capacitor keyboard events
        this.keyboardPlugin.addListener('keyboardWillShow', (info) => {
            this.keyboardHeight = info.keyboardHeight;
            this.handleKeyboardShow();
        });

        this.keyboardPlugin.addListener('keyboardWillHide', () => {
            this.keyboardHeight = 0;
            this.handleKeyboardHide();
        });

        // Configure keyboard behavior
        this.keyboardPlugin.setAccessoryBarVisible({ isVisible: false });
        this.keyboardPlugin.setScroll({ isDisabled: false });
        this.keyboardPlugin.setStyle({ style: 'DARK' });
        this.keyboardPlugin.setResizeMode({ mode: 'ionic' });
    }

    setupKeyboardEvents() {
        // Handle focus events
        document.addEventListener('focusin', (e) => {
            if (this.isInputElement(e.target)) {
                this.activeInput = e.target;
                this.handleInputFocus(e.target);
            }
        });

        document.addEventListener('focusout', (e) => {
            if (this.isInputElement(e.target)) {
                this.activeInput = null;
                this.handleInputBlur(e.target);
            }
        });

        // Handle input events for dynamic resizing
        document.addEventListener('input', (e) => {
            if (this.isInputElement(e.target) && e.target === this.activeInput) {
                this.adjustInputHeight(e.target);
            }
        });
    }

    setupInputHandling() {
        // Prevent zoom on input focus
        const inputs = document.querySelectorAll('input, textarea, select');
        inputs.forEach(input => {
            this.configureInput(input);
        });

        // Handle dynamically added inputs
        const observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                mutation.addedNodes.forEach((node) => {
                    if (node.nodeType === 1) {
                        if (this.isInputElement(node)) {
                            this.configureInput(node);
                        }
                        const childInputs = node.querySelectorAll('input, textarea, select');
                        childInputs.forEach(input => this.configureInput(input));
                    }
                });
            });
        });

        observer.observe(document.body, { childList: true, subtree: true });
    }

    setupViewportHandling() {
        // Handle viewport changes for keyboard
        if (window.visualViewport) {
            window.visualViewport.addEventListener('resize', () => {
                this.handleViewportResize();
            });
        }

        // Handle orientation changes
        window.addEventListener('orientationchange', () => {
            setTimeout(() => {
                this.resetViewport();
                if (this.activeInput) {
                    this.scrollToInput(this.activeInput);
                }
            }, 500);
        });
    }

    configureInput(input) {
        // Ensure font size is at least 16px to prevent zoom
        const computedStyle = window.getComputedStyle(input);
        const fontSize = parseFloat(computedStyle.fontSize);
        
        if (fontSize < 16) {
            input.style.fontSize = '16px';
        }

        // Add iOS-specific attributes
        input.setAttribute('autocomplete', input.getAttribute('autocomplete') || 'off');
        input.setAttribute('autocorrect', input.getAttribute('autocorrect') || 'off');
        input.setAttribute('autocapitalize', input.getAttribute('autocapitalize') || 'off');
        input.setAttribute('spellcheck', input.getAttribute('spellcheck') || 'false');

        // Configure specific input types
        if (input.type === 'email') {
            input.setAttribute('inputmode', 'email');
        } else if (input.type === 'tel') {
            input.setAttribute('inputmode', 'tel');
        } else if (input.type === 'number') {
            input.setAttribute('inputmode', 'numeric');
        }
    }

    handleInputFocus(input) {
        // Prevent zoom by temporarily adjusting viewport
        if (this.originalViewport) {
            const viewport = this.originalViewport.cloneNode();
            viewport.setAttribute('content', 
                'width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no');
            this.originalViewport.parentNode.replaceChild(viewport, this.originalViewport);
        }

        // Scroll input into view with delay for keyboard animation
        setTimeout(() => {
            this.scrollToInput(input);
        }, 300);

        // Add focused class for styling
        input.classList.add('ios-input-focused');

        // Trigger haptic feedback
        if (window.iOSHaptics) {
            window.iOSHaptics.light();
        }
    }

    handleInputBlur(input) {
        // Restore original viewport
        if (this.originalViewport) {
            const currentViewport = document.querySelector('meta[name=viewport]');
            if (currentViewport) {
                currentViewport.setAttribute('content', 
                    'width=device-width, initial-scale=1.0');
            }
        }

        // Remove focused class
        input.classList.remove('ios-input-focused');

        // Reset any input height adjustments
        if (input.tagName === 'TEXTAREA') {
            input.style.height = '';
        }
    }

    handleKeyboardShow() {
        document.body.classList.add('keyboard-visible');
        
        // Adjust layout for keyboard
        if (this.keyboardHeight > 0) {
            document.documentElement.style.setProperty('--keyboard-height', `${this.keyboardHeight}px`);
        }

        // Scroll active input into view
        if (this.activeInput) {
            setTimeout(() => {
                this.scrollToInput(this.activeInput);
            }, 100);
        }
    }

    handleKeyboardHide() {
        document.body.classList.remove('keyboard-visible');
        document.documentElement.style.removeProperty('--keyboard-height');
        
        // Reset viewport
        this.resetViewport();
    }

    handleViewportResize() {
        if (this.activeInput && window.visualViewport) {
            const keyboardHeight = window.innerHeight - window.visualViewport.height;
            if (keyboardHeight > 100) { // Keyboard is likely visible
                this.keyboardHeight = keyboardHeight;
                this.handleKeyboardShow();
            } else {
                this.handleKeyboardHide();
            }
        }
    }

    scrollToInput(input) {
        if (!input) return;

        const inputRect = input.getBoundingClientRect();
        const viewportHeight = window.visualViewport ? window.visualViewport.height : window.innerHeight;
        const keyboardHeight = this.keyboardHeight || 0;
        const availableHeight = viewportHeight - keyboardHeight;
        
        // Calculate if input is hidden behind keyboard
        const inputBottom = inputRect.bottom;
        const visibleBottom = availableHeight;
        
        if (inputBottom > visibleBottom) {
            const scrollAmount = inputBottom - visibleBottom + 20; // 20px padding
            window.scrollBy({
                top: scrollAmount,
                behavior: 'smooth'
            });
        }
    }

    adjustInputHeight(input) {
        if (input.tagName !== 'TEXTAREA') return;

        // Auto-resize textarea
        input.style.height = 'auto';
        input.style.height = `${input.scrollHeight}px`;
        
        // Ensure it doesn't exceed max height
        const maxHeight = window.innerHeight * 0.3; // 30% of viewport
        if (input.scrollHeight > maxHeight) {
            input.style.height = `${maxHeight}px`;
            input.style.overflowY = 'scroll';
        } else {
            input.style.overflowY = 'hidden';
        }
    }

    resetViewport() {
        if (this.originalViewport) {
            const currentViewport = document.querySelector('meta[name=viewport]');
            if (currentViewport) {
                currentViewport.setAttribute('content', 
                    this.originalViewport.getAttribute('content') || 
                    'width=device-width, initial-scale=1.0');
            }
        }
    }

    isInputElement(element) {
        const inputTypes = ['INPUT', 'TEXTAREA', 'SELECT'];
        return inputTypes.includes(element.tagName) || 
               element.contentEditable === 'true' ||
               element.hasAttribute('contenteditable');
    }

    // Public methods
    hideKeyboard() {
        if (this.keyboardPlugin) {
            this.keyboardPlugin.hide();
        } else if (this.activeInput) {
            this.activeInput.blur();
        }
    }

    showKeyboard() {
        if (this.keyboardPlugin) {
            this.keyboardPlugin.show();
        }
    }
}

// Auto-initialize when DOM is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        window.iOSKeyboard = new iOSKeyboard();
    });
} else {
    window.iOSKeyboard = new iOSKeyboard();
}

// Export for module use
if (typeof module !== 'undefined' && module.exports) {
    module.exports = iOSKeyboard;
}
