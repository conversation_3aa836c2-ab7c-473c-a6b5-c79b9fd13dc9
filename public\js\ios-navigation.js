/**
 * iOS Navigation Patterns
 * Implements iOS-style navigation including swipe-back gestures and page transitions
 */

class iOSNavigation {
    constructor() {
        this.isIOS = this.detectIOS();
        this.navigationHistory = [];
        this.currentPage = null;
        this.isTransitioning = false;
        this.swipeThreshold = 50;
        this.swipeStartX = 0;
        this.swipeStartY = 0;
        this.isSwipeBack = false;

        this.init();
    }

    detectIOS() {
        return /iPad|iPhone|iPod/.test(navigator.userAgent) || 
               (navigator.platform === 'MacIntel' && navigator.maxTouchPoints > 1) ||
               window.Capacitor?.getPlatform() === 'ios';
    }

    init() {
        console.log('🧭 Initializing iOS navigation patterns...');

        this.setupSwipeBackGesture();
        this.setupPageTransitions();
        this.setupNavigationEvents();
        this.trackCurrentPage();
        
        console.log('✅ iOS navigation patterns initialized');
    }

    setupSwipeBackGesture() {
        // iOS-style swipe from left edge to go back
        document.addEventListener('touchstart', this.handleSwipeStart.bind(this), { passive: true });
        document.addEventListener('touchmove', this.handleSwipeMove.bind(this), { passive: false });
        document.addEventListener('touchend', this.handleSwipeEnd.bind(this), { passive: true });
        document.addEventListener('touchcancel', this.handleSwipeCancel.bind(this), { passive: true });
    }

    setupPageTransitions() {
        // Intercept navigation clicks for smooth transitions
        document.addEventListener('click', (e) => {
            const link = e.target.closest('a[href], [data-navigate]');
            if (link && this.shouldInterceptNavigation(link)) {
                e.preventDefault();
                this.navigateWithTransition(link);
            }
        });

        // Handle browser back/forward
        window.addEventListener('popstate', (e) => {
            if (e.state && e.state.page) {
                this.handleBrowserNavigation(e.state.page);
            }
        });
    }

    setupNavigationEvents() {
        // Listen for custom navigation events
        document.addEventListener('navigateTo', (e) => {
            this.navigateTo(e.detail.url, e.detail.options);
        });

        document.addEventListener('goBack', () => {
            this.goBack();
        });

        // Handle modal navigation
        document.addEventListener('modalOpened', (e) => {
            this.pushModalState(e.detail.modalId);
        });

        document.addEventListener('modalClosed', (e) => {
            this.popModalState(e.detail.modalId);
        });
    }

    trackCurrentPage() {
        // Track current page in navigation history
        const currentPath = window.location.pathname + window.location.search;
        this.currentPage = currentPath;
        
        if (this.navigationHistory.length === 0 || 
            this.navigationHistory[this.navigationHistory.length - 1] !== currentPath) {
            this.navigationHistory.push(currentPath);
        }

        // Update browser history state
        if (window.history.state === null) {
            window.history.replaceState({ page: currentPath }, '', currentPath);
        }
    }

    handleSwipeStart(e) {
        // Only trigger on left edge of screen
        const touch = e.touches[0];
        this.swipeStartX = touch.clientX;
        this.swipeStartY = touch.clientY;
        
        // Check if swipe started from left edge (within 20px)
        if (this.swipeStartX <= 20 && this.canGoBack()) {
            this.isSwipeBack = true;
            
            // Add visual feedback
            document.body.classList.add('swipe-back-active');
            
            // Trigger haptic feedback
            if (window.iOSHaptics) {
                window.iOSHaptics.light();
            }
        }
    }

    handleSwipeMove(e) {
        if (!this.isSwipeBack) return;

        const touch = e.touches[0];
        const deltaX = touch.clientX - this.swipeStartX;
        const deltaY = touch.clientY - this.swipeStartY;
        
        // Check if it's a horizontal swipe (not vertical scroll)
        if (Math.abs(deltaY) > Math.abs(deltaX)) {
            this.cancelSwipeBack();
            return;
        }

        // Prevent default scrolling during swipe
        if (deltaX > 0) {
            e.preventDefault();
            
            // Calculate swipe progress (0 to 1)
            const progress = Math.min(deltaX / window.innerWidth, 1);
            
            // Apply visual feedback
            this.updateSwipeBackVisual(progress);
            
            // Trigger threshold haptic
            if (deltaX > this.swipeThreshold && !this.swipeThresholdReached) {
                this.swipeThresholdReached = true;
                if (window.iOSHaptics) {
                    window.iOSHaptics.medium();
                }
            }
        }
    }

    handleSwipeEnd(e) {
        if (!this.isSwipeBack) return;

        const touch = e.changedTouches[0];
        const deltaX = touch.clientX - this.swipeStartX;
        
        if (deltaX > this.swipeThreshold) {
            // Complete swipe back
            this.completeSwipeBack();
        } else {
            // Cancel swipe back
            this.cancelSwipeBack();
        }
        
        this.resetSwipeState();
    }

    handleSwipeCancel(e) {
        if (this.isSwipeBack) {
            this.cancelSwipeBack();
            this.resetSwipeState();
        }
    }

    updateSwipeBackVisual(progress) {
        // Apply iOS-style swipe back visual effect
        const currentPage = document.querySelector('.page-content, main, .main-content');
        if (currentPage) {
            const translateX = progress * 100;
            const scale = 1 - (progress * 0.05); // Slight scale effect
            
            currentPage.style.transform = `translateX(${translateX}%) scale(${scale})`;
            currentPage.style.filter = `brightness(${1 - progress * 0.2})`;
        }
    }

    completeSwipeBack() {
        // Animate completion
        const currentPage = document.querySelector('.page-content, main, .main-content');
        if (currentPage) {
            currentPage.style.transition = 'transform 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94)';
            currentPage.style.transform = 'translateX(100%)';
            
            setTimeout(() => {
                this.goBack();
                this.resetSwipeVisual();
            }, 300);
        } else {
            this.goBack();
        }
    }

    cancelSwipeBack() {
        // Animate back to original position
        const currentPage = document.querySelector('.page-content, main, .main-content');
        if (currentPage) {
            currentPage.style.transition = 'transform 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94)';
            currentPage.style.transform = 'translateX(0) scale(1)';
            currentPage.style.filter = 'brightness(1)';
            
            setTimeout(() => {
                this.resetSwipeVisual();
            }, 300);
        }
    }

    resetSwipeState() {
        this.isSwipeBack = false;
        this.swipeThresholdReached = false;
        document.body.classList.remove('swipe-back-active');
    }

    resetSwipeVisual() {
        const currentPage = document.querySelector('.page-content, main, .main-content');
        if (currentPage) {
            currentPage.style.transition = '';
            currentPage.style.transform = '';
            currentPage.style.filter = '';
        }
    }

    shouldInterceptNavigation(link) {
        // Don't intercept external links, downloads, or special protocols
        const href = link.getAttribute('href') || link.getAttribute('data-navigate');
        
        if (!href) return false;
        if (href.startsWith('http') && !href.includes(window.location.hostname)) return false;
        if (href.startsWith('mailto:') || href.startsWith('tel:')) return false;
        if (link.hasAttribute('download')) return false;
        if (link.getAttribute('target') === '_blank') return false;
        
        return true;
    }

    async navigateWithTransition(link) {
        if (this.isTransitioning) return;

        const href = link.getAttribute('href') || link.getAttribute('data-navigate');
        const transitionType = link.getAttribute('data-transition') || 'slide';
        
        this.isTransitioning = true;
        
        // Trigger haptic feedback
        if (window.iOSHaptics) {
            window.iOSHaptics.light();
        }

        try {
            await this.performPageTransition(href, transitionType);
        } catch (error) {
            console.error('Navigation transition failed:', error);
            // Fallback to normal navigation
            window.location.href = href;
        }
        
        this.isTransitioning = false;
    }

    async performPageTransition(url, transitionType = 'slide') {
        // Add transition class
        document.body.classList.add(`transition-${transitionType}`);
        
        // Dispatch transition start event
        document.dispatchEvent(new CustomEvent('pageTransitionStart', {
            detail: { url, transitionType }
        }));

        // Perform the navigation
        if (url.startsWith('#')) {
            // Hash navigation (modal or section)
            this.handleHashNavigation(url);
        } else {
            // Full page navigation
            this.navigationHistory.push(url);
            window.history.pushState({ page: url }, '', url);
            
            // For SPA navigation, you would load content here
            // For now, we'll do a full page load
            window.location.href = url;
        }
    }

    handleHashNavigation(hash) {
        // Handle hash-based navigation (modals, sections)
        const target = document.querySelector(hash);
        if (target) {
            if (target.classList.contains('modal')) {
                // Open modal
                this.openModal(target);
            } else {
                // Scroll to section
                target.scrollIntoView({ behavior: 'smooth' });
            }
        }
    }

    handleBrowserNavigation(page) {
        // Handle browser back/forward navigation
        this.currentPage = page;
        
        // Update navigation history
        const historyIndex = this.navigationHistory.indexOf(page);
        if (historyIndex !== -1) {
            this.navigationHistory = this.navigationHistory.slice(0, historyIndex + 1);
        }
    }

    goBack() {
        if (this.canGoBack()) {
            // Remove current page from history
            this.navigationHistory.pop();
            
            // Get previous page
            const previousPage = this.navigationHistory[this.navigationHistory.length - 1];
            
            if (previousPage) {
                window.history.back();
            } else {
                // No previous page, go to home
                this.navigateTo('/');
            }
            
            // Trigger haptic feedback
            if (window.iOSHaptics) {
                window.iOSHaptics.light();
            }
        }
    }

    canGoBack() {
        return this.navigationHistory.length > 1;
    }

    navigateTo(url, options = {}) {
        const transitionType = options.transition || 'slide';
        this.performPageTransition(url, transitionType);
    }

    pushModalState(modalId) {
        const modalUrl = `${window.location.pathname}#${modalId}`;
        this.navigationHistory.push(modalUrl);
        window.history.pushState({ page: modalUrl, modal: modalId }, '', modalUrl);
    }

    popModalState(modalId) {
        if (this.navigationHistory.length > 0) {
            const lastUrl = this.navigationHistory[this.navigationHistory.length - 1];
            if (lastUrl.includes(`#${modalId}`)) {
                this.navigationHistory.pop();
                window.history.back();
            }
        }
    }

    openModal(modal) {
        modal.classList.add('show');
        document.body.classList.add('modal-open');
        
        // Dispatch modal opened event
        document.dispatchEvent(new CustomEvent('modalOpened', {
            detail: { modalId: modal.id }
        }));
    }

    // Public methods
    getCurrentPage() {
        return this.currentPage;
    }

    getNavigationHistory() {
        return [...this.navigationHistory];
    }

    clearHistory() {
        this.navigationHistory = [this.currentPage];
    }
}

// Auto-initialize when DOM is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        window.iOSNavigation = new iOSNavigation();
    });
} else {
    window.iOSNavigation = new iOSNavigation();
}

// Export for module use
if (typeof module !== 'undefined' && module.exports) {
    module.exports = iOSNavigation;
}
