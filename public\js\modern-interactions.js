// Modern Social Media Interactions for Naroop - 2025 Standards

class ModernInteractions {
    constructor() {
        this.isScrolling = false;
        this.lastScrollTop = 0;
        this.posts = [];
        this.currentPage = 1;
        this.isLoading = false;
        this.init();
    }

    init() {
        this.setupInfiniteScroll();
        this.setupCardInteractions();
        this.setupMicroInteractions();
        this.setupTouchGestures();
        this.setupPullToRefresh();
        this.setupLazyLoading();
        this.setupKeyboardShortcuts();
    }

    setupInfiniteScroll() {
        const contentArea = document.querySelector('.content-area');
        if (!contentArea) return;

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting && !this.isLoading) {
                    this.loadMorePosts();
                }
            });
        }, {
            rootMargin: '100px'
        });

        // Create load more trigger element
        const loadTrigger = document.createElement('div');
        loadTrigger.className = 'load-more-trigger';
        loadTrigger.style.height = '1px';
        
        const postsContainer = document.getElementById('postsContainer');
        if (postsContainer) {
            postsContainer.appendChild(loadTrigger);
            observer.observe(loadTrigger);
        }
    }

    async loadMorePosts() {
        if (this.isLoading) return;
        
        this.isLoading = true;
        this.showLoadingState();

        // Simulate API call
        await new Promise(resolve => setTimeout(resolve, 1000));

        const newPosts = this.generateMockPosts(5);
        this.renderPosts(newPosts);
        
        this.isLoading = false;
        this.hideLoadingState();
        this.currentPage++;
    }

    generateMockPosts(count) {
        const users = [
            { name: 'Alex Rivera', avatar: '👨🏽', time: '3 hours ago' },
            { name: 'Zara Williams', avatar: '👩🏿', time: '5 hours ago' },
            { name: 'Marcus Johnson', avatar: '👨🏾', time: '7 hours ago' },
            { name: 'Nia Thompson', avatar: '👩🏾', time: '9 hours ago' },
            { name: 'Jordan Davis', avatar: '👨🏿', time: '12 hours ago' }
        ];

        const posts = [
            "Starting my own business has been the most challenging yet rewarding journey. Every setback taught me something valuable. 💪 #Entrepreneurship #BlackBusiness",
            "Celebrating 5 years at my dream job today! Hard work and persistence really do pay off. Grateful for this journey. 🎉 #CareerGoals #Success",
            "Just finished reading an amazing book that completely changed my perspective. Knowledge truly is power! 📚 #Reading #Growth",
            "Community garden project is finally complete! So proud of what we accomplished together. 🌱 #Community #Teamwork",
            "Mentoring young professionals has been incredibly fulfilling. Giving back feels so good! 🤝 #Mentorship #GivingBack"
        ];

        return Array.from({ length: count }, (_, i) => ({
            id: Date.now() + i,
            user: users[i % users.length],
            content: posts[i % posts.length],
            likes: Math.floor(Math.random() * 200) + 10,
            comments: Math.floor(Math.random() * 50) + 2,
            shares: Math.floor(Math.random() * 20) + 1
        }));
    }

    renderPosts(posts) {
        const postsContainer = document.getElementById('postsContainer');
        if (!postsContainer) return;

        posts.forEach(post => {
            const postElement = this.createPostElement(post);
            postsContainer.insertBefore(postElement, postsContainer.lastElementChild);
            
            // Animate in
            setTimeout(() => {
                postElement.classList.add('fade-in-up');
            }, 100);
        });
    }

    createPostElement(post) {
        const article = document.createElement('article');
        article.className = 'feed-card';
        article.innerHTML = `
            <div class="feed-card-header">
                <div class="feed-card-avatar">${post.user.avatar}</div>
                <div class="feed-card-user-info">
                    <h4 class="feed-card-username">${post.user.name}</h4>
                    <p class="feed-card-timestamp">${post.user.time}</p>
                </div>
                <button class="btn-base btn-sm btn-outline">Follow</button>
            </div>
            <div class="feed-card-content">
                <p>${post.content}</p>
            </div>
            <div class="feed-card-actions">
                <button class="feed-action-btn like-btn" data-post-id="${post.id}">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z"></path>
                    </svg>
                    <span class="like-count">${post.likes}</span>
                </button>
                <button class="feed-action-btn comment-btn" data-post-id="${post.id}">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"></path>
                    </svg>
                    <span>${post.comments}</span>
                </button>
                <button class="feed-action-btn share-btn" data-post-id="${post.id}">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <circle cx="18" cy="5" r="3"></circle>
                        <circle cx="6" cy="12" r="3"></circle>
                        <circle cx="18" cy="19" r="3"></circle>
                        <line x1="8.59" y1="13.51" x2="15.42" y2="17.49"></line>
                        <line x1="15.41" y1="6.51" x2="8.59" y2="10.49"></line>
                    </svg>
                    <span>${post.shares}</span>
                </button>
            </div>
        `;

        this.setupPostInteractions(article);
        return article;
    }

    setupCardInteractions() {
        // Setup interactions for existing cards
        document.querySelectorAll('.feed-card').forEach(card => {
            this.setupPostInteractions(card);
        });
    }

    setupPostInteractions(card) {
        // Like button interaction
        const likeBtn = card.querySelector('.like-btn');
        if (likeBtn) {
            likeBtn.addEventListener('click', (e) => {
                e.preventDefault();
                this.handleLike(likeBtn);
            });
        }

        // Comment button interaction
        const commentBtn = card.querySelector('.comment-btn');
        if (commentBtn) {
            commentBtn.addEventListener('click', (e) => {
                e.preventDefault();
                this.handleComment(commentBtn);
            });
        }

        // Share button interaction
        const shareBtn = card.querySelector('.share-btn');
        if (shareBtn) {
            shareBtn.addEventListener('click', (e) => {
                e.preventDefault();
                this.handleShare(shareBtn);
            });
        }

        // Double tap to like
        let lastTap = 0;
        card.addEventListener('touchend', (e) => {
            const currentTime = new Date().getTime();
            const tapLength = currentTime - lastTap;
            if (tapLength < 500 && tapLength > 0) {
                this.handleDoubleTapLike(card);
            }
            lastTap = currentTime;
        });
    }

    handleLike(button) {
        const isLiked = button.classList.contains('liked');
        const countSpan = button.querySelector('.like-count');
        let count = parseInt(countSpan.textContent);

        if (isLiked) {
            button.classList.remove('liked');
            count--;
            this.animateUnlike(button);
        } else {
            button.classList.add('liked');
            count++;
            this.animateLike(button);
        }

        countSpan.textContent = count;
        
        // Haptic feedback
        if ('vibrate' in navigator) {
            navigator.vibrate(50);
        }
    }

    animateLike(button) {
        // Heart animation
        const heart = button.querySelector('svg');
        heart.style.animation = 'heartBeat 0.6s ease-in-out';
        
        // Create floating hearts
        this.createFloatingHearts(button);
        
        setTimeout(() => {
            heart.style.animation = '';
        }, 600);
    }

    animateUnlike(button) {
        const heart = button.querySelector('svg');
        heart.style.animation = 'heartShrink 0.3s ease-in-out';
        
        setTimeout(() => {
            heart.style.animation = '';
        }, 300);
    }

    createFloatingHearts(button) {
        for (let i = 0; i < 3; i++) {
            const heart = document.createElement('div');
            heart.innerHTML = '❤️';
            heart.className = 'floating-heart';
            heart.style.cssText = `
                position: absolute;
                font-size: 1.2rem;
                pointer-events: none;
                z-index: 1000;
                animation: floatHeart 2s ease-out forwards;
                animation-delay: ${i * 0.1}s;
            `;
            
            const rect = button.getBoundingClientRect();
            heart.style.left = rect.left + Math.random() * 20 + 'px';
            heart.style.top = rect.top + 'px';
            
            document.body.appendChild(heart);
            
            setTimeout(() => {
                heart.remove();
            }, 2000);
        }
    }

    handleDoubleTapLike(card) {
        const likeBtn = card.querySelector('.like-btn');
        if (likeBtn && !likeBtn.classList.contains('liked')) {
            this.handleLike(likeBtn);
            
            // Show large heart animation
            const bigHeart = document.createElement('div');
            bigHeart.innerHTML = '❤️';
            bigHeart.className = 'big-heart-animation';
            bigHeart.style.cssText = `
                position: absolute;
                font-size: 4rem;
                pointer-events: none;
                z-index: 1000;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                animation: bigHeartPop 1s ease-out forwards;
            `;
            
            card.style.position = 'relative';
            card.appendChild(bigHeart);
            
            setTimeout(() => {
                bigHeart.remove();
            }, 1000);
        }
    }

    handleComment(button) {
        console.log('Opening comment modal...');
        // Would open comment modal in real implementation
    }

    handleShare(button) {
        if (navigator.share) {
            navigator.share({
                title: 'Naroop Post',
                text: 'Check out this post on Naroop!',
                url: window.location.href
            });
        } else {
            // Fallback - copy to clipboard
            navigator.clipboard.writeText(window.location.href);
            this.showToast('Link copied to clipboard!');
        }
    }

    setupMicroInteractions() {
        // Add ripple effect to buttons
        document.addEventListener('click', (e) => {
            if (e.target.matches('.btn-base, .nav-item, .mobile-nav-item, .feed-action-btn')) {
                this.createRipple(e);
            }
        });

        // Add hover effects to cards
        document.addEventListener('mouseover', (e) => {
            if (e.target.closest('.feed-card')) {
                e.target.closest('.feed-card').classList.add('hovered');
            }
        });

        document.addEventListener('mouseout', (e) => {
            if (e.target.closest('.feed-card')) {
                e.target.closest('.feed-card').classList.remove('hovered');
            }
        });
    }

    createRipple(e) {
        const button = e.target.closest('.btn-base, .nav-item, .mobile-nav-item, .feed-action-btn');
        if (!button) return;

        const ripple = document.createElement('span');
        const rect = button.getBoundingClientRect();
        const size = Math.max(rect.width, rect.height);
        const x = e.clientX - rect.left - size / 2;
        const y = e.clientY - rect.top - size / 2;

        ripple.style.cssText = `
            position: absolute;
            width: ${size}px;
            height: ${size}px;
            left: ${x}px;
            top: ${y}px;
            background: rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            transform: scale(0);
            animation: ripple 0.6s linear;
            pointer-events: none;
        `;

        button.style.position = 'relative';
        button.style.overflow = 'hidden';
        button.appendChild(ripple);

        setTimeout(() => {
            ripple.remove();
        }, 600);
    }

    setupTouchGestures() {
        // Already implemented in navigation-enhanced.js
        // This could be extended for more gestures
    }

    setupPullToRefresh() {
        let startY = 0;
        let currentY = 0;
        let isPulling = false;

        const contentArea = document.querySelector('.content-area');
        if (!contentArea) return;

        contentArea.addEventListener('touchstart', (e) => {
            if (contentArea.scrollTop === 0) {
                startY = e.touches[0].clientY;
                isPulling = true;
            }
        });

        contentArea.addEventListener('touchmove', (e) => {
            if (!isPulling) return;

            currentY = e.touches[0].clientY;
            const pullDistance = currentY - startY;

            if (pullDistance > 0 && pullDistance < 100) {
                e.preventDefault();
                this.showPullToRefreshIndicator(pullDistance);
            }
        });

        contentArea.addEventListener('touchend', () => {
            if (isPulling && currentY - startY > 80) {
                this.refreshFeed();
            }
            this.hidePullToRefreshIndicator();
            isPulling = false;
        });
    }

    showPullToRefreshIndicator(distance) {
        let indicator = document.querySelector('.pull-to-refresh');
        if (!indicator) {
            indicator = document.createElement('div');
            indicator.className = 'pull-to-refresh';
            indicator.innerHTML = '↓ Pull to refresh';
            document.querySelector('.content-area').prepend(indicator);
        }
        
        indicator.style.transform = `translateY(${Math.min(distance, 80)}px)`;
        indicator.style.opacity = Math.min(distance / 80, 1);
    }

    hidePullToRefreshIndicator() {
        const indicator = document.querySelector('.pull-to-refresh');
        if (indicator) {
            indicator.style.transform = 'translateY(-100%)';
            indicator.style.opacity = '0';
        }
    }

    async refreshFeed() {
        const indicator = document.querySelector('.pull-to-refresh');
        if (indicator) {
            indicator.innerHTML = '🔄 Refreshing...';
            indicator.classList.add('active');
        }

        // Simulate refresh
        await new Promise(resolve => setTimeout(resolve, 1500));

        // Reset feed
        const postsContainer = document.getElementById('postsContainer');
        if (postsContainer) {
            const existingPosts = postsContainer.querySelectorAll('.feed-card');
            existingPosts.forEach(post => post.remove());
            
            const newPosts = this.generateMockPosts(3);
            this.renderPosts(newPosts);
        }

        this.showToast('Feed refreshed!');
        this.hidePullToRefreshIndicator();
    }

    setupLazyLoading() {
        const imageObserver = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const img = entry.target;
                    img.src = img.dataset.src;
                    img.classList.remove('lazy');
                    imageObserver.unobserve(img);
                }
            });
        });

        document.querySelectorAll('img[data-src]').forEach(img => {
            imageObserver.observe(img);
        });
    }

    setupKeyboardShortcuts() {
        document.addEventListener('keydown', (e) => {
            if (e.target.matches('input, textarea')) return;

            switch(e.key) {
                case 'j':
                    this.scrollToNextPost();
                    break;
                case 'k':
                    this.scrollToPreviousPost();
                    break;
                case 'l':
                    this.likeCurrentPost();
                    break;
                case 'r':
                    this.refreshFeed();
                    break;
            }
        });
    }

    scrollToNextPost() {
        const posts = document.querySelectorAll('.feed-card');
        const currentPost = this.getCurrentVisiblePost();
        const currentIndex = Array.from(posts).indexOf(currentPost);
        
        if (currentIndex < posts.length - 1) {
            posts[currentIndex + 1].scrollIntoView({ behavior: 'smooth', block: 'center' });
        }
    }

    scrollToPreviousPost() {
        const posts = document.querySelectorAll('.feed-card');
        const currentPost = this.getCurrentVisiblePost();
        const currentIndex = Array.from(posts).indexOf(currentPost);
        
        if (currentIndex > 0) {
            posts[currentIndex - 1].scrollIntoView({ behavior: 'smooth', block: 'center' });
        }
    }

    getCurrentVisiblePost() {
        const posts = document.querySelectorAll('.feed-card');
        const viewportCenter = window.innerHeight / 2;
        
        for (const post of posts) {
            const rect = post.getBoundingClientRect();
            if (rect.top <= viewportCenter && rect.bottom >= viewportCenter) {
                return post;
            }
        }
        
        return posts[0];
    }

    likeCurrentPost() {
        const currentPost = this.getCurrentVisiblePost();
        if (currentPost) {
            const likeBtn = currentPost.querySelector('.like-btn');
            if (likeBtn) {
                this.handleLike(likeBtn);
            }
        }
    }

    showLoadingState() {
        const loading = document.getElementById('feedLoading');
        if (loading) {
            loading.style.display = 'block';
        }
    }

    hideLoadingState() {
        const loading = document.getElementById('feedLoading');
        if (loading) {
            loading.style.display = 'none';
        }
    }

    showToast(message) {
        const toast = document.createElement('div');
        toast.className = 'toast';
        toast.textContent = message;
        toast.style.cssText = `
            position: fixed;
            bottom: 100px;
            left: 50%;
            transform: translateX(-50%);
            background: var(--surface-color);
            color: var(--text-primary);
            padding: 12px 24px;
            border-radius: 24px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
            z-index: 1000;
            animation: toastSlideUp 0.3s ease-out;
        `;

        document.body.appendChild(toast);

        setTimeout(() => {
            toast.style.animation = 'toastSlideDown 0.3s ease-out forwards';
            setTimeout(() => toast.remove(), 300);
        }, 2000);
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.modernInteractions = new ModernInteractions();
});

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ModernInteractions;
}
