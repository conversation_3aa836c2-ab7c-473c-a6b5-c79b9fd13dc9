<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Naroop</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
            color: white;
            height: 100vh;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            overflow: hidden;
        }
        
        .splash-container {
            text-align: center;
            animation: fadeInUp 1s ease-out;
        }
        
        .logo {
            width: 120px;
            height: 120px;
            background: linear-gradient(135deg, #f59e0b 0%, #f97316 50%, #d97706 100%);
            border-radius: 26px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 24px;
            box-shadow: 0 10px 30px rgba(245, 158, 11, 0.3);
            position: relative;
            overflow: hidden;
        }
        
        .logo::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 60%;
            background: linear-gradient(180deg, rgba(255,255,255,0.3) 0%, rgba(255,255,255,0) 100%);
            border-radius: 26px 26px 0 0;
        }
        
        .logo-text {
            font-size: 54px;
            font-weight: 800;
            color: white;
            text-shadow: 0 2px 4px rgba(0,0,0,0.2);
            z-index: 1;
            position: relative;
        }
        
        .app-name {
            font-size: 32px;
            font-weight: 600;
            margin-bottom: 8px;
            letter-spacing: -0.5px;
        }
        
        .tagline {
            font-size: 16px;
            font-weight: 500;
            color: #f59e0b;
            opacity: 0.9;
            margin-bottom: 40px;
        }
        
        .loading-indicator {
            width: 40px;
            height: 40px;
            border: 3px solid rgba(245, 158, 11, 0.3);
            border-top: 3px solid #f59e0b;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto;
        }
        
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        /* iOS safe area support */
        @supports (padding: max(0px)) {
            body {
                padding-top: max(20px, env(safe-area-inset-top));
                padding-bottom: max(20px, env(safe-area-inset-bottom));
            }
        }
        
        /* Dark mode support */
        @media (prefers-color-scheme: dark) {
            body {
                background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
            }
        }
        
        /* Responsive design */
        @media (max-width: 480px) {
            .logo {
                width: 100px;
                height: 100px;
                border-radius: 22px;
            }
            
            .logo-text {
                font-size: 45px;
            }
            
            .app-name {
                font-size: 28px;
            }
            
            .tagline {
                font-size: 14px;
            }
        }
    </style>
</head>
<body>
    <div class="splash-container">
        <div class="logo">
            <div class="logo-text">N</div>
        </div>
        <h1 class="app-name">Naroop</h1>
        <p class="tagline">Narrative of Our People</p>
        <div class="loading-indicator"></div>
    </div>
    
    <script>
        // Auto-redirect to main app after loading
        setTimeout(() => {
            if (window.location.pathname === '/splash-screen.html') {
                window.location.href = '/';
            }
        }, 2000);
        
        // Hide splash screen when main app is ready
        document.addEventListener('DOMContentLoaded', () => {
            if (window.parent !== window) {
                // Running in iframe, hide after short delay
                setTimeout(() => {
                    document.body.style.opacity = '0';
                    setTimeout(() => {
                        window.parent.postMessage('splashComplete', '*');
                    }, 300);
                }, 1500);
            }
        });
    </script>
</body>
</html>
