const fs = require('fs');
const path = require('path');

// Create a sophisticated SVG icon generator for Naroop
function generateSVGIcon(size, text = 'N') {
    const cornerRadius = size * 0.22; // iOS-style corner radius
    const fontSize = size * 0.45;
    const shadowOffset = size * 0.02;

    return `<?xml version="1.0" encoding="UTF-8"?>
<svg width="${size}" height="${size}" viewBox="0 0 ${size} ${size}" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- Main gradient -->
    <linearGradient id="mainGrad${size}" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#f59e0b;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#f97316;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#d97706;stop-opacity:1" />
    </linearGradient>

    <!-- Highlight gradient -->
    <linearGradient id="highlight${size}" x1="0%" y1="0%" x2="100%" y2="50%">
      <stop offset="0%" style="stop-color:#ffffff;stop-opacity:0.3" />
      <stop offset="100%" style="stop-color:#ffffff;stop-opacity:0" />
    </linearGradient>

    <!-- Shadow filter -->
    <filter id="shadow${size}" x="-20%" y="-20%" width="140%" height="140%">
      <feDropShadow dx="0" dy="${shadowOffset}" stdDeviation="${size * 0.01}" flood-color="#000000" flood-opacity="0.3"/>
    </filter>
  </defs>

  <!-- Main background -->
  <rect width="${size}" height="${size}" rx="${cornerRadius}" fill="url(#mainGrad${size})" filter="url(#shadow${size})"/>

  <!-- Highlight overlay -->
  <rect width="${size}" height="${size * 0.6}" rx="${cornerRadius}" fill="url(#highlight${size})"/>

  <!-- Text shadow -->
  <text x="50%" y="${50 + shadowOffset}%" dominant-baseline="middle" text-anchor="middle"
        font-family="SF Pro Display, -apple-system, Arial, sans-serif" font-weight="700"
        font-size="${fontSize}" fill="#000000" opacity="0.2">${text}</text>

  <!-- Main text -->
  <text x="50%" y="50%" dominant-baseline="middle" text-anchor="middle"
        font-family="SF Pro Display, -apple-system, Arial, sans-serif" font-weight="700"
        font-size="${fontSize}" fill="white">${text}</text>
</svg>`;
}

// Generate iOS App Store icon (special design)
function generateAppStoreIcon() {
    const size = 1024;
    const cornerRadius = size * 0.22;

    return `<?xml version="1.0" encoding="UTF-8"?>
<svg width="${size}" height="${size}" viewBox="0 0 ${size} ${size}" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="appStoreGrad" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#f59e0b;stop-opacity:1" />
      <stop offset="25%" style="stop-color:#f97316;stop-opacity:1" />
      <stop offset="75%" style="stop-color:#d97706;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#b45309;stop-opacity:1" />
    </linearGradient>

    <linearGradient id="appStoreHighlight" x1="0%" y1="0%" x2="100%" y2="40%">
      <stop offset="0%" style="stop-color:#ffffff;stop-opacity:0.4" />
      <stop offset="100%" style="stop-color:#ffffff;stop-opacity:0" />
    </linearGradient>

    <filter id="appStoreShadow" x="-10%" y="-10%" width="120%" height="120%">
      <feDropShadow dx="0" dy="8" stdDeviation="12" flood-color="#000000" flood-opacity="0.25"/>
    </filter>
  </defs>

  <!-- Main background -->
  <rect width="${size}" height="${size}" rx="${cornerRadius}" fill="url(#appStoreGrad)" filter="url(#appStoreShadow)"/>

  <!-- Highlight -->
  <rect width="${size}" height="${size * 0.5}" rx="${cornerRadius}" fill="url(#appStoreHighlight)"/>

  <!-- Logo design -->
  <g transform="translate(${size/2}, ${size/2})">
    <!-- Letter N with modern design -->
    <text x="0" y="20" dominant-baseline="middle" text-anchor="middle"
          font-family="SF Pro Display, -apple-system, Arial, sans-serif" font-weight="800"
          font-size="420" fill="#000000" opacity="0.15">N</text>
    <text x="0" y="0" dominant-baseline="middle" text-anchor="middle"
          font-family="SF Pro Display, -apple-system, Arial, sans-serif" font-weight="800"
          font-size="420" fill="white">N</text>
  </g>
</svg>`;
}

// iOS App Icon sizes (all required sizes)
const iOSIconSizes = [
    { size: 1024, name: 'AppIcon-1024x1024', desc: 'App Store' },
    { size: 180, name: 'AppIcon-180x180', desc: 'iPhone App (60pt @3x)' },
    { size: 167, name: 'AppIcon-167x167', desc: 'iPad Pro App (83.5pt @2x)' },
    { size: 152, name: 'AppIcon-152x152', desc: 'iPad App (76pt @2x)' },
    { size: 120, name: 'AppIcon-120x120', desc: 'iPhone App (60pt @2x)' },
    { size: 87, name: 'AppIcon-87x87', desc: 'iPhone Settings (29pt @3x)' },
    { size: 80, name: 'AppIcon-80x80', desc: 'iPhone Spotlight (40pt @2x)' },
    { size: 76, name: 'AppIcon-76x76', desc: 'iPad App (76pt @1x)' },
    { size: 58, name: 'AppIcon-58x58', desc: 'iPhone Settings (29pt @2x)' },
    { size: 40, name: 'AppIcon-40x40', desc: 'iPhone Spotlight (40pt @1x)' },
    { size: 29, name: 'AppIcon-29x29', desc: 'iPhone Settings (29pt @1x)' },
    { size: 20, name: 'AppIcon-20x20', desc: 'iPhone Notification (20pt @1x)' }
];

// PWA icon sizes
const pwaIconSizes = [72, 96, 128, 144, 192, 384, 512];

const iconsDir = path.join(__dirname, '..', 'public', 'images', 'icons');
const iOSIconsDir = path.join(__dirname, '..', 'ios', 'App', 'App', 'Assets.xcassets', 'AppIcon.appiconset');

// Ensure directories exist
[iconsDir, iOSIconsDir].forEach(dir => {
    if (!fs.existsSync(dir)) {
        fs.mkdirSync(dir, { recursive: true });
    }
});

console.log('🎨 Generating Naroop app icons...');

// Generate iOS App Icons
console.log('\n📱 Generating iOS App Icons:');
iOSIconSizes.forEach(({ size, name, desc }) => {
    const svgContent = size === 1024 ? generateAppStoreIcon() : generateSVGIcon(size);
    const filename = `${name}.svg`;
    const filepath = path.join(iconsDir, filename);

    fs.writeFileSync(filepath, svgContent);
    console.log(`✅ Generated ${filename} (${desc})`);

    // Also save to iOS assets directory
    const iOSFilepath = path.join(iOSIconsDir, filename);
    fs.writeFileSync(iOSFilepath, svgContent);
});

// Generate PWA Icons
console.log('\n🌐 Generating PWA Icons:');
pwaIconSizes.forEach(size => {
    const svgContent = generateSVGIcon(size);
    const filename = `icon-${size}x${size}.svg`;
    const filepath = path.join(iconsDir, filename);

    fs.writeFileSync(filepath, svgContent);
    console.log(`✅ Generated ${filename}`);
});

// Generate shortcut icons
console.log('\n⚡ Generating Shortcut Icons:');
const shortcutIcons = [
    { name: 'shortcut-post.svg', text: '+', size: 96 },
    { name: 'shortcut-stories.svg', text: '📖', size: 96 }
];

shortcutIcons.forEach(({ name, text, size }) => {
    const svgContent = generateSVGIcon(size, text);
    const filepath = path.join(iconsDir, name);

    fs.writeFileSync(filepath, svgContent);
    console.log(`✅ Generated ${name}`);
});

// Generate iOS App Icon Contents.json
const contentsJson = {
    "images": iOSIconSizes.map(({ size, name, desc }) => ({
        "filename": `${name}.svg`,
        "idiom": size >= 167 ? "ipad" : "iphone",
        "scale": size >= 180 ? "3x" : size >= 120 ? "2x" : "1x",
        "size": `${Math.floor(size / (size >= 180 ? 3 : size >= 120 ? 2 : 1))}x${Math.floor(size / (size >= 180 ? 3 : size >= 120 ? 2 : 1))}`
    })),
    "info": {
        "author": "Naroop Icon Generator",
        "version": 1
    }
};

fs.writeFileSync(
    path.join(iOSIconsDir, 'Contents.json'),
    JSON.stringify(contentsJson, null, 2)
);

console.log('\n🎉 All icons generated successfully!');
console.log('📱 iOS App Icons: Ready for Xcode');
console.log('🌐 PWA Icons: Ready for web');
console.log('📝 Note: These are high-quality SVG icons that will work great for testing.');
console.log('💡 For production, consider creating custom PNG icons with your actual logo.');
